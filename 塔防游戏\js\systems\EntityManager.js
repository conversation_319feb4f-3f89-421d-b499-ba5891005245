/**
 * 实体管理器
 * 管理所有游戏对象的生命周期
 */

class EntityManager {
  constructor() {
    // 实体存储
    this.entities = new Map()  // 所有实体
    this.towers = new Map()    // 塔
    this.enemies = new Map()   // 敌人
    this.bullets = new Map()   // 子弹
    this.effects = new Map()   // 特效
    
    // 对象池
    this.bulletPool = []
    this.effectPool = []
    
    // 性能统计
    this.stats = {
      totalEntities: 0,
      activeTowers: 0,
      activeEnemies: 0,
      activeBullets: 0,
      activeEffects: 0
    }
    
    // 清理计数器
    this.cleanupCounter = 0
    this.cleanupInterval = 60  // 每60帧清理一次死亡对象
  }
  
  /**
   * 添加实体
   * @param {BaseEntity} entity - 要添加的实体
   */
  addEntity(entity) {
    if (!entity || !entity.id) {
      console.error('尝试添加无效实体')
      return false
    }
    
    // 添加到总实体列表
    this.entities.set(entity.id, entity)
    
    // 根据类型添加到对应列表
    switch (entity.type) {
      case 'tower':
        this.towers.set(entity.id, entity)
        break
      case 'enemy':
        this.enemies.set(entity.id, entity)
        break
      case 'bullet':
        this.bullets.set(entity.id, entity)
        break
      case 'effect':
        this.effects.set(entity.id, entity)
        break
    }
    
    this.updateStats()
    return true
  }
  
  /**
   * 移除实体
   * @param {string} entityId - 实体ID
   */
  removeEntity(entityId) {
    const entity = this.entities.get(entityId)
    if (!entity) {
      return false
    }
    
    // 从总列表移除
    this.entities.delete(entityId)
    
    // 从对应类型列表移除
    switch (entity.type) {
      case 'tower':
        this.towers.delete(entityId)
        break
      case 'enemy':
        this.enemies.delete(entityId)
        break
      case 'bullet':
        this.bullets.delete(entityId)
        // 回收到对象池
        this.recycleBullet(entity)
        break
      case 'effect':
        this.effects.delete(entityId)
        // 回收到对象池
        this.recycleEffect(entity)
        break
    }
    
    // 清理实体
    if (entity.cleanup) {
      entity.cleanup()
    }
    
    this.updateStats()
    return true
  }
  
  /**
   * 获取实体
   * @param {string} entityId - 实体ID
   */
  getEntity(entityId) {
    return this.entities.get(entityId)
  }
  
  /**
   * 获取所有塔
   */
  getTowers() {
    return Array.from(this.towers.values())
  }
  
  /**
   * 获取所有敌人
   */
  getEnemies() {
    return Array.from(this.enemies.values())
  }
  
  /**
   * 获取所有子弹
   */
  getBullets() {
    return Array.from(this.bullets.values())
  }
  
  /**
   * 获取所有特效
   */
  getEffects() {
    return Array.from(this.effects.values())
  }
  
  /**
   * 获取活跃的敌人（用于塔的目标选择）
   */
  getActiveEnemies() {
    return Array.from(this.enemies.values()).filter(enemy => enemy.isAlive)
  }
  
  /**
   * 在指定范围内查找敌人
   * @param {number} x - 中心x坐标
   * @param {number} y - 中心y坐标
   * @param {number} radius - 搜索半径
   */
  getEnemiesInRadius(x, y, radius) {
    const enemies = []
    const radiusSquared = radius * radius
    
    for (const enemy of this.enemies.values()) {
      if (!enemy.isAlive) continue
      
      const dx = enemy.x - x
      const dy = enemy.y - y
      const distanceSquared = dx * dx + dy * dy
      
      if (distanceSquared <= radiusSquared) {
        enemies.push(enemy)
      }
    }
    
    return enemies
  }
  
  /**
   * 在指定位置查找塔
   * @param {number} x - x坐标
   * @param {number} y - y坐标
   * @param {number} tolerance - 容差范围
   */
  getTowerAt(x, y, tolerance = 20) {
    for (const tower of this.towers.values()) {
      const distance = Math.sqrt((tower.x - x) ** 2 + (tower.y - y) ** 2)
      if (distance <= tolerance) {
        return tower
      }
    }
    return null
  }
  
  /**
   * 从对象池获取子弹
   */
  getBulletFromPool() {
    if (this.bulletPool.length > 0) {
      return this.bulletPool.pop()
    }
    return null
  }
  
  /**
   * 回收子弹到对象池
   */
  recycleBullet(bullet) {
    if (this.bulletPool.length < 100) {  // 限制对象池大小
      bullet.isAlive = false
      bullet.isVisible = false
      this.bulletPool.push(bullet)
    }
  }
  
  /**
   * 从对象池获取特效
   */
  getEffectFromPool() {
    if (this.effectPool.length > 0) {
      return this.effectPool.pop()
    }
    return null
  }
  
  /**
   * 回收特效到对象池
   */
  recycleEffect(effect) {
    if (this.effectPool.length < 50) {  // 限制对象池大小
      effect.isAlive = false
      effect.isVisible = false
      this.effectPool.push(effect)
    }
  }
  
  /**
   * 更新所有实体
   * @param {number} deltaTime - 时间增量
   */
  update(deltaTime) {
    // 更新所有实体
    for (const entity of this.entities.values()) {
      if (entity.isAlive && entity.update) {
        entity.update(deltaTime)
      }
    }
    
    // 定期清理死亡对象
    this.cleanupCounter++
    if (this.cleanupCounter >= this.cleanupInterval) {
      this.cleanupDeadEntities()
      this.cleanupCounter = 0
    }
  }
  
  /**
   * 渲染所有实体
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   */
  render(ctx) {
    // 按层次渲染
    this.renderLayer(ctx, this.effects.values())  // 背景特效
    this.renderLayer(ctx, this.towers.values())   // 塔
    this.renderLayer(ctx, this.enemies.values())  // 敌人
    this.renderLayer(ctx, this.bullets.values())  // 子弹
    // 前景特效可以在这里添加
  }
  
  /**
   * 渲染指定层的实体
   */
  renderLayer(ctx, entities) {
    for (const entity of entities) {
      if (entity.isVisible && entity.render) {
        entity.render(ctx)
      }
    }
  }
  
  /**
   * 清理死亡的实体
   */
  cleanupDeadEntities() {
    const deadEntities = []
    
    // 收集死亡的实体
    for (const [id, entity] of this.entities) {
      if (!entity.isAlive) {
        deadEntities.push(id)
      }
    }
    
    // 移除死亡的实体
    for (const id of deadEntities) {
      this.removeEntity(id)
    }
  }
  
  /**
   * 清理所有实体
   */
  clear() {
    // 清理所有实体
    for (const entity of this.entities.values()) {
      if (entity.cleanup) {
        entity.cleanup()
      }
    }
    
    // 清空所有容器
    this.entities.clear()
    this.towers.clear()
    this.enemies.clear()
    this.bullets.clear()
    this.effects.clear()
    
    // 清空对象池
    this.bulletPool.length = 0
    this.effectPool.length = 0
    
    this.updateStats()
  }
  
  /**
   * 更新统计信息
   */
  updateStats() {
    this.stats.totalEntities = this.entities.size
    this.stats.activeTowers = this.towers.size
    this.stats.activeEnemies = this.enemies.size
    this.stats.activeBullets = this.bullets.size
    this.stats.activeEffects = this.effects.size
  }
  
  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats }
  }
  
  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    return {
      entities: this.entities.size,
      bulletPool: this.bulletPool.length,
      effectPool: this.effectPool.length,
      totalMemoryObjects: this.entities.size + this.bulletPool.length + this.effectPool.length
    }
  }
  
  /**
   * 检查位置是否被占用
   * @param {number} x - x坐标
   * @param {number} y - y坐标
   * @param {number} radius - 检查半径
   */
  isPositionOccupied(x, y, radius = 20) {
    for (const tower of this.towers.values()) {
      const distance = Math.sqrt((tower.x - x) ** 2 + (tower.y - y) ** 2)
      if (distance < radius + tower.radius) {
        return true
      }
    }
    return false
  }
  
  /**
   * 获取指定类型的实体数量
   * @param {string} type - 实体类型
   */
  getEntityCount(type) {
    switch (type) {
      case 'tower': return this.towers.size
      case 'enemy': return this.enemies.size
      case 'bullet': return this.bullets.size
      case 'effect': return this.effects.size
      default: return this.entities.size
    }
  }
  
  /**
   * 暂停所有实体
   */
  pause() {
    for (const entity of this.entities.values()) {
      if (entity.pause) {
        entity.pause()
      }
    }
  }
  
  /**
   * 恢复所有实体
   */
  resume() {
    for (const entity of this.entities.values()) {
      if (entity.resume) {
        entity.resume()
      }
    }
  }
}

// 导出模块
module.exports = EntityManager
