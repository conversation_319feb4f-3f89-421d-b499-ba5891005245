# 塔防游戏问题排查指南

## 🚨 常见错误及解决方案

### 1. 模块加载错误
```
Error: module 'js/xxx/xxx.js' is not defined
```

**原因**：文件路径错误或文件不存在

**解决方案**：
1. 检查文件是否存在
2. 确认require路径是否正确
3. 检查文件名大小写是否匹配

### 2. 语法错误
```
Unexpected token
```

**原因**：JavaScript语法错误

**解决方案**：
1. 检查大括号是否匹配
2. 检查分号和逗号
3. 使用IDE的语法检查功能

### 3. 场景切换失败
```
Cannot read property 'xxx' of undefined
```

**原因**：场景对象未正确初始化

**解决方案**：
1. 检查场景构造函数
2. 确认所有依赖文件存在
3. 检查参数传递是否正确

### 4. 触摸事件无响应

**原因**：事件绑定失败或Canvas设置问题

**解决方案**：
1. 检查wx.onTouchStart绑定
2. 确认Canvas尺寸设置正确
3. 检查ScreenAdapter配置

### 5. 图片或资源加载失败

**原因**：资源文件路径错误

**解决方案**：
1. 检查assets文件夹
2. 确认图片文件存在
3. 检查ImageManager配置

## 🔍 调试步骤

### 第一步：检查控制台
1. 打开微信开发者工具
2. 查看控制台(Console)标签
3. 记录所有错误信息

### 第二步：逐步排查
1. 从最简单的功能开始测试
2. 逐个添加功能模块
3. 每次添加后测试是否正常

### 第三步：文件完整性检查
确认以下核心文件存在：

#### 必需文件
- [x] js/main.js
- [x] js/utils/ScreenAdapter.js
- [x] js/utils/ImageManager.js
- [x] js/components/BackButton.js
- [x] js/scenes/LoginScene.js
- [x] js/scenes/AdventureMapScene.js
- [x] js/scenes/TestBattleScene.js

#### 配置文件
- [x] js/config/LevelConfig.js
- [x] js/config/TowerConfig.js
- [x] js/config/EnemyConfig.js

#### 战斗系统文件
- [x] js/entities/BaseEntity.js
- [x] js/entities/Tower.js
- [x] js/entities/Enemy.js
- [x] js/entities/Bullet.js
- [x] js/systems/EntityManager.js
- [x] js/systems/GameMap.js
- [x] js/systems/CombatSystem.js
- [x] js/ui/BattleUI.js
- [x] js/scenes/BattleScene.js

## 🛠️ 修复方法

### 重新创建缺失文件
如果某个文件缺失，可以从以下位置重新创建：

1. **基础文件**：参考现有的LoginScene.js结构
2. **配置文件**：使用简化的配置数据
3. **实体文件**：从BaseEntity.js扩展

### 简化测试
如果完整系统有问题，可以：

1. **使用TestBattleScene**：简化版本，更容易调试
2. **逐步添加功能**：从基础功能开始
3. **分模块测试**：单独测试每个系统

### 回退方案
如果新功能有问题：

1. **注释新代码**：暂时禁用有问题的功能
2. **使用备份**：恢复到之前的工作版本
3. **分步实现**：将复杂功能分解为简单步骤

## 📋 检查清单

### 启动检查
- [ ] 游戏能正常启动
- [ ] 登录页面显示正常
- [ ] 场景切换正常

### 基础功能检查
- [ ] 触摸事件响应
- [ ] UI按钮工作
- [ ] 场景切换正常

### 战斗系统检查
- [ ] 能进入战斗场景
- [ ] 基础UI显示
- [ ] 触摸建造功能
- [ ] 敌人生成和移动
- [ ] 攻击和战斗逻辑

## 🆘 紧急修复

### 如果游戏完全无法启动

1. **检查game.js**：确认入口文件正确
2. **检查main.js**：确认主程序无语法错误
3. **简化代码**：注释掉新添加的功能

### 如果只是战斗系统有问题

1. **使用TestBattleScene**：切换到简化版本
2. **检查依赖**：确认所有require的文件存在
3. **逐步调试**：从最简单的功能开始

### 最小可运行版本

如果所有都失败，可以创建最小版本：

```javascript
// 最简单的战斗场景
class MinimalBattleScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
  }
  
  update(deltaTime) {
    // 空实现
  }
  
  render() {
    this.ctx.fillStyle = '#90EE90'
    this.ctx.fillRect(0, 0, 800, 600)
    
    this.ctx.fillStyle = '#000000'
    this.ctx.font = '24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText('战斗系统测试', 400, 300)
  }
  
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  destroy() {}
}
```

## 📞 获取帮助

### 调试信息收集
遇到问题时，请收集以下信息：

1. **错误信息**：完整的控制台错误
2. **操作步骤**：重现问题的具体步骤
3. **环境信息**：微信开发者工具版本
4. **文件状态**：哪些文件存在/缺失

### 常用调试代码

```javascript
// 在关键位置添加调试信息
console.log('场景初始化完成')
console.log('当前状态:', this.gameState)
console.log('实体数量:', this.entities.length)

// 检查对象是否存在
if (!this.someObject) {
  console.error('对象未初始化:', 'someObject')
}

// 检查函数是否存在
if (typeof this.someFunction !== 'function') {
  console.error('函数不存在:', 'someFunction')
}
```

通过这个排查指南，应该能够解决大部分常见问题！
