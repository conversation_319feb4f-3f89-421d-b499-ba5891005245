/**
 * 测试战斗场景
 * 简化版本的战斗场景，用于测试基础功能
 */
const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')
const AudioManager = require('../utils/AudioManager.js')
const DataManager = require('../utils/DataManager.js')

class TestBattleScene {
  constructor(canvas, ctx, levelId = 1) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 创建返回按钮
    this.backButtonComponent = new BackButton(require('../utils/ImageManager.js'))
    
    // 游戏状态
    this.levelId = levelId
    this.gameState = 'playing'
    this.isPaused = false
    
    // 资源
    this.gold = 200
    this.lives = 20
    this.score = 0
    this.currentWave = 1
    this.totalWaves = 3
    
    // 简单的实体列表
    this.towers = []
    this.enemies = []
    this.bullets = []
    
    // 简单的地图
    this.mapWidth = 800
    this.mapHeight = 600
    this.path = [
      { x: 0, y: 300 },
      { x: 200, y: 300 },
      { x: 200, y: 150 },
      { x: 600, y: 150 },
      { x: 600, y: 450 },
      { x: 800, y: 450 }
    ]
    
    // 测试用的敌人生成
    this.lastEnemySpawn = 0
    this.enemySpawnInterval = 2000
    this.enemiesSpawned = 0
    this.maxEnemies = 5
    
    // 绑定事件
    this.bindEvents()

    // 播放背景音乐
    this.playBackgroundMusic()

    console.log('测试战斗场景初始化完成')
  }

  /**
   * 播放背景音乐
   */
  playBackgroundMusic() {
    const settings = DataManager.getSettings()
    if (settings.musicEnabled) {
      AudioManager.playMusic('battle_theme', {
        volume: settings.musicVolume,
        loop: true
      })
    }
  }

  /**
   * 播放音效
   */
  playSfx(soundId, options = {}) {
    const settings = DataManager.getSettings()
    if (settings.sfxEnabled) {
      AudioManager.playSfx(soundId, {
        volume: settings.sfxVolume,
        ...options
      })
    }
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    
    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
      return
    }
    
    // 简单的塔建造测试
    this.tryBuildTower(pos.x, pos.y)
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    // 检查返回按钮
    if (this.backButtonComponent.isPressed() && this.backButtonComponent.isPointInButton(pos)) {
      this.onBackClick()
      this.backButtonComponent.setPressed(false)
      return
    }
    this.backButtonComponent.setPressed(false)

    // 尝试建造塔
    this.tryBuildTower(pos.x, pos.y)
  }
  
  /**
   * 尝试建造塔
   */
  tryBuildTower(x, y) {
    // 简单的建造逻辑
    if (this.gold >= 50) {
      const tower = {
        x: x,
        y: y,
        range: 80,
        damage: 25,
        lastAttack: 0,
        attackSpeed: 1000,
        target: null
      }
      
      this.towers.push(tower)
      this.gold -= 50

      // 播放建造音效
      this.playSfx('build_tower')

      // 记录统计
      DataManager.recordTowerBuilt()

      wx.showToast({
        title: '建造成功',
        icon: 'success'
      })
    } else {
      wx.showToast({
        title: '金币不足',
        icon: 'none'
      })
    }
  }
  
  /**
   * 生成敌人
   */
  spawnEnemy() {
    if (this.enemiesSpawned >= this.maxEnemies) return
    
    const enemy = {
      x: this.path[0].x,
      y: this.path[0].y,
      hp: 100,
      maxHp: 100,
      speed: 50,
      pathIndex: 0,
      pathProgress: 0
    }
    
    this.enemies.push(enemy)
    this.enemiesSpawned++
  }
  
  /**
   * 更新敌人
   */
  updateEnemies(deltaTime) {
    for (let i = this.enemies.length - 1; i >= 0; i--) {
      const enemy = this.enemies[i]
      
      // 移动敌人
      if (enemy.pathIndex < this.path.length - 1) {
        const currentPoint = this.path[enemy.pathIndex]
        const nextPoint = this.path[enemy.pathIndex + 1]
        
        const dx = nextPoint.x - currentPoint.x
        const dy = nextPoint.y - currentPoint.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        
        if (distance > 0) {
          const moveDistance = enemy.speed * deltaTime / 1000
          const moveX = (dx / distance) * moveDistance
          const moveY = (dy / distance) * moveDistance
          
          enemy.x += moveX
          enemy.y += moveY
          
          // 检查是否到达下一个路径点
          const distToNext = Math.sqrt((nextPoint.x - enemy.x) ** 2 + (nextPoint.y - enemy.y) ** 2)
          if (distToNext < 10) {
            enemy.pathIndex++
            enemy.x = nextPoint.x
            enemy.y = nextPoint.y
          }
        }
      } else {
        // 到达终点
        this.lives--
        this.enemies.splice(i, 1)
      }
      
      // 检查死亡
      if (enemy.hp <= 0) {
        this.gold += 10
        this.score += 10
        this.enemies.splice(i, 1)

        // 播放死亡音效
        this.playSfx('enemy_death', { volume: 0.5 })

        // 记录击杀
        DataManager.recordEnemyKill()
      }
    }
  }
  
  /**
   * 更新塔
   */
  updateTowers(deltaTime) {
    const currentTime = Date.now()
    
    for (const tower of this.towers) {
      // 寻找目标
      tower.target = null
      let minDistance = tower.range
      
      for (const enemy of this.enemies) {
        const distance = Math.sqrt((enemy.x - tower.x) ** 2 + (enemy.y - tower.y) ** 2)
        if (distance < minDistance) {
          minDistance = distance
          tower.target = enemy
        }
      }
      
      // 攻击目标
      if (tower.target && currentTime - tower.lastAttack > tower.attackSpeed) {
        tower.target.hp -= tower.damage
        tower.lastAttack = currentTime

        // 播放攻击音效
        this.playSfx('attack_arrow', { volume: 0.6 })

        // 创建简单的子弹效果
        this.bullets.push({
          x: tower.x,
          y: tower.y,
          targetX: tower.target.x,
          targetY: tower.target.y,
          speed: 300,
          life: 1000
        })
      }
    }
  }
  
  /**
   * 更新子弹
   */
  updateBullets(deltaTime) {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      const bullet = this.bullets[i]
      
      // 移动子弹
      const dx = bullet.targetX - bullet.x
      const dy = bullet.targetY - bullet.y
      const distance = Math.sqrt(dx * dx + dy * dy)
      
      if (distance > 5) {
        const moveDistance = bullet.speed * deltaTime / 1000
        bullet.x += (dx / distance) * moveDistance
        bullet.y += (dy / distance) * moveDistance
      } else {
        // 子弹到达目标
        this.bullets.splice(i, 1)
      }
      
      // 检查生命周期
      bullet.life -= deltaTime
      if (bullet.life <= 0) {
        this.bullets.splice(i, 1)
      }
    }
  }
  
  /**
   * 返回按钮点击
   */
  onBackClick() {
    if (this.onSceneChange) {
      this.onSceneChange('adventure-map')
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    if (this.isPaused) return
    
    // 生成敌人
    const currentTime = Date.now()
    if (currentTime - this.lastEnemySpawn > this.enemySpawnInterval) {
      this.spawnEnemy()
      this.lastEnemySpawn = currentTime
    }
    
    // 更新游戏对象
    this.updateEnemies(deltaTime)
    this.updateTowers(deltaTime)
    this.updateBullets(deltaTime)
    
    // 检查胜利条件
    if (this.enemiesSpawned >= this.maxEnemies && this.enemies.length === 0) {
      // 停止背景音乐并播放胜利音效
      AudioManager.stopMusic(1000)
      this.playSfx('victory')

      wx.showModal({
        title: '测试完成',
        content: '基础功能测试通过！',
        showCancel: false,
        success: () => {
          this.onBackClick()
        }
      })
    }

    // 检查失败条件
    if (this.lives <= 0) {
      // 停止背景音乐并播放失败音效
      AudioManager.stopMusic(500)
      this.playSfx('defeat')

      wx.showModal({
        title: '测试失败',
        content: '生命值耗尽',
        showCancel: false,
        success: () => {
          this.onBackClick()
        }
      })
    }
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 清空画布
    this.ctx.clearRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
    
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())
    
    // 绘制背景
    this.ctx.fillStyle = '#90EE90'
    this.ctx.fillRect(0, 0, this.mapWidth, this.mapHeight)
    
    // 绘制路径
    this.ctx.strokeStyle = '#8B4513'
    this.ctx.lineWidth = 20
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'
    
    this.ctx.beginPath()
    this.ctx.moveTo(this.path[0].x, this.path[0].y)
    for (let i = 1; i < this.path.length; i++) {
      this.ctx.lineTo(this.path[i].x, this.path[i].y)
    }
    this.ctx.stroke()
    
    // 绘制塔
    for (const tower of this.towers) {
      this.ctx.fillStyle = '#8B4513'
      this.ctx.beginPath()
      this.ctx.arc(tower.x, tower.y, 15, 0, Math.PI * 2)
      this.ctx.fill()
      
      // 绘制攻击范围
      if (tower.target) {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
        this.ctx.lineWidth = 1
        this.ctx.beginPath()
        this.ctx.arc(tower.x, tower.y, tower.range, 0, Math.PI * 2)
        this.ctx.stroke()
      }
    }
    
    // 绘制敌人
    for (const enemy of this.enemies) {
      this.ctx.fillStyle = '#ff0000'
      this.ctx.beginPath()
      this.ctx.arc(enemy.x, enemy.y, 10, 0, Math.PI * 2)
      this.ctx.fill()
      
      // 绘制血条
      if (enemy.hp < enemy.maxHp) {
        const barWidth = 20
        const barHeight = 4
        const barX = enemy.x - barWidth / 2
        const barY = enemy.y - 20
        
        this.ctx.fillStyle = '#333333'
        this.ctx.fillRect(barX, barY, barWidth, barHeight)
        
        const healthPercent = enemy.hp / enemy.maxHp
        this.ctx.fillStyle = healthPercent > 0.5 ? '#00ff00' : '#ff0000'
        this.ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight)
      }
    }
    
    // 绘制子弹
    for (const bullet of this.bullets) {
      this.ctx.fillStyle = '#ffff00'
      this.ctx.beginPath()
      this.ctx.arc(bullet.x, bullet.y, 3, 0, Math.PI * 2)
      this.ctx.fill()
    }
    
    // 绘制UI
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(10, 10, 200, 80)
    
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'top'
    
    this.ctx.fillText(`金币: ${this.gold}`, 20, 25)
    this.ctx.fillText(`生命: ${this.lives}`, 20, 45)
    this.ctx.fillText(`分数: ${this.score}`, 20, 65)
    
    // 绘制说明
    this.ctx.fillText('点击空地建造塔 (50金币)', 20, this.mapHeight - 40)
    
    // 绘制返回按钮
    this.backButtonComponent.draw(this.ctx)
    
    this.ctx.restore()
  }
  
  /**
   * 销毁场景
   */
  destroy() {
    // 停止音乐和音效
    AudioManager.stopMusic()
    AudioManager.stopAllSfx()

    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    this.touchStartHandler = null
    this.touchEndHandler = null
  }
}

// 导出模块
module.exports = TestBattleScene
