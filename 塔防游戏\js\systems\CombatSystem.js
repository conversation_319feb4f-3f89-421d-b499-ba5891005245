/**
 * 战斗系统
 * 处理攻击、伤害计算、效果应用等核心战斗逻辑
 */

class CombatSystem {
  constructor() {
    this.damageEvents = []  // 伤害事件队列
    this.effectManager = null  // 效果管理器
  }
  
  /**
   * 设置效果管理器
   */
  setEffectManager(effectManager) {
    this.effectManager = effectManager
  }
  
  /**
   * 塔攻击敌人
   */
  towerAttack(tower, enemy) {
    if (!this.canAttack(tower, enemy)) {
      return false
    }
    
    // 检查攻击间隔
    const currentTime = Date.now()
    const attackInterval = 1000 / tower.attackSpeed  // 转换为毫秒
    
    if (currentTime - tower.lastAttackTime < attackInterval) {
      return false
    }
    
    // 执行攻击
    tower.lastAttackTime = currentTime
    
    // 创建子弹或直接造成伤害
    if (tower.bulletType === 'instant') {
      this.applyDamage(tower, enemy)
    } else {
      this.createBullet(tower, enemy)
    }
    
    // 触发攻击事件
    this.onTowerAttack(tower, enemy)
    
    return true
  }
  
  /**
   * 检查是否可以攻击
   */
  canAttack(tower, enemy) {
    // 检查距离
    const distance = this.calculateDistance(tower, enemy)
    if (distance > tower.range) {
      return false
    }
    
    // 检查目标类型
    if (enemy.type === 'air' && !tower.canTargetAir) {
      return false
    }
    
    if (enemy.type === 'ground' && !tower.canTargetGround) {
      return false
    }
    
    // 检查敌人是否存活
    if (!enemy.isAlive || enemy.hp <= 0) {
      return false
    }
    
    return true
  }
  
  /**
   * 创建子弹
   */
  createBullet(tower, enemy) {
    const bulletConfig = {
      x: tower.x,
      y: tower.y,
      target: enemy,
      damage: tower.damage,
      speed: tower.bulletSpeed || 200,
      type: tower.bulletType || 'arrow',
      effects: tower.effects || []
    }
    
    // 通过事件系统创建子弹
    this.onBulletCreate(bulletConfig)
  }
  
  /**
   * 子弹命中敌人
   */
  bulletHit(bullet, enemy) {
    // 应用伤害
    this.applyDamage(bullet, enemy)
    
    // 应用特殊效果
    if (bullet.effects) {
      bullet.effects.forEach(effect => {
        this.applyEffect(enemy, effect)
      })
    }
    
    // 处理溅射伤害
    if (bullet.splashRadius && bullet.splashRadius > 0) {
      this.applySplashDamage(bullet, enemy)
    }
    
    // 销毁子弹
    bullet.destroy()
  }
  
  /**
   * 应用伤害
   */
  applyDamage(attacker, target) {
    const damage = this.calculateDamage(attacker, target)
    
    // 扣除生命值
    target.hp -= damage.final
    
    // 记录伤害事件
    this.damageEvents.push({
      attacker: attacker,
      target: target,
      damage: damage,
      timestamp: Date.now()
    })
    
    // 检查死亡
    if (target.hp <= 0) {
      this.onEnemyDeath(target)
    }
    
    // 触发伤害事件
    this.onDamageDealt(attacker, target, damage)
    
    return damage
  }
  
  /**
   * 计算伤害
   */
  calculateDamage(attacker, target) {
    let baseDamage = attacker.damage || 0
    let armor = target.armor || 0
    let magicResist = target.magicResist || 0
    
    // 根据伤害类型计算
    let finalDamage = baseDamage
    
    if (attacker.damageType === 'physical') {
      // 物理伤害：护甲减免
      finalDamage = Math.max(1, baseDamage - armor)
      
      // 物理抗性
      if (target.resistances && target.resistances.physical) {
        finalDamage *= (1 - target.resistances.physical)
      }
    } else if (attacker.damageType === 'magic') {
      // 魔法伤害：魔抗减免
      finalDamage = baseDamage * (1 - magicResist / 100)
      
      // 魔法抗性
      if (target.resistances && target.resistances.magic) {
        finalDamage *= (1 - target.resistances.magic)
      }
    }
    
    // 暴击计算
    let isCritical = false
    if (attacker.criticalChance && Math.random() < attacker.criticalChance) {
      isCritical = true
      finalDamage *= (attacker.criticalMultiplier || 2)
    }
    
    // 确保最小伤害
    finalDamage = Math.max(1, Math.floor(finalDamage))
    
    return {
      base: baseDamage,
      final: finalDamage,
      blocked: baseDamage - finalDamage,
      isCritical: isCritical,
      type: attacker.damageType || 'physical'
    }
  }
  
  /**
   * 应用溅射伤害
   */
  applySplashDamage(bullet, centerTarget) {
    const splashTargets = this.findEnemiesInRadius(
      centerTarget.x, 
      centerTarget.y, 
      bullet.splashRadius
    )
    
    splashTargets.forEach(enemy => {
      if (enemy !== centerTarget && enemy.isAlive) {
        const splashAttacker = {
          damage: bullet.damage * (bullet.splashDamage || 0.5),
          damageType: bullet.damageType
        }
        this.applyDamage(splashAttacker, enemy)
      }
    })
  }
  
  /**
   * 应用效果
   */
  applyEffect(target, effect) {
    if (!this.effectManager) return
    
    switch (effect.type) {
      case 'slow':
        this.effectManager.addSlowEffect(target, effect.strength, effect.duration)
        break
        
      case 'poison':
        this.effectManager.addPoisonEffect(target, effect.damage, effect.duration)
        break
        
      case 'freeze':
        this.effectManager.addFreezeEffect(target, effect.duration)
        break
        
      case 'armor_reduction':
        this.effectManager.addArmorReductionEffect(target, effect.amount, effect.duration)
        break
    }
  }
  
  /**
   * 计算两点距离
   */
  calculateDistance(obj1, obj2) {
    const dx = obj1.x - obj2.x
    const dy = obj1.y - obj2.y
    return Math.sqrt(dx * dx + dy * dy)
  }
  
  /**
   * 查找半径内的敌人
   */
  findEnemiesInRadius(x, y, radius) {
    // 这个方法需要从外部注入敌人列表
    if (this.getEnemiesInRadius) {
      return this.getEnemiesInRadius(x, y, radius)
    }
    return []
  }
  
  /**
   * 敌人死亡处理
   */
  onEnemyDeath(enemy) {
    // 给予奖励
    if (this.onRewardGained) {
      this.onRewardGained({
        gold: enemy.reward || 0,
        exp: enemy.exp || 0
      })
    }
    
    // 标记为死亡
    enemy.isAlive = false
    
    // 触发死亡事件
    if (this.onEnemyKilled) {
      this.onEnemyKilled(enemy)
    }
  }
  
  /**
   * 获取伤害统计
   */
  getDamageStats() {
    const stats = {
      totalDamage: 0,
      physicalDamage: 0,
      magicDamage: 0,
      criticalHits: 0,
      totalHits: 0
    }
    
    this.damageEvents.forEach(event => {
      stats.totalDamage += event.damage.final
      stats.totalHits++
      
      if (event.damage.type === 'physical') {
        stats.physicalDamage += event.damage.final
      } else if (event.damage.type === 'magic') {
        stats.magicDamage += event.damage.final
      }
      
      if (event.damage.isCritical) {
        stats.criticalHits++
      }
    })
    
    return stats
  }
  
  /**
   * 清理伤害事件历史
   */
  clearDamageHistory() {
    this.damageEvents = []
  }
  
  /**
   * 事件回调 - 需要外部设置
   */
  onTowerAttack(tower, enemy) {}
  onBulletCreate(bulletConfig) {}
  onDamageDealt(attacker, target, damage) {}
  onEnemyKilled(enemy) {}
  onRewardGained(reward) {}
}

/**
 * 效果管理系统
 * 管理各种临时效果（减速、中毒、冰冻等）
 */
class EffectManager {
  constructor() {
    this.activeEffects = new Map()  // 按目标分组的效果列表
  }

  /**
   * 添加减速效果
   */
  addSlowEffect(target, strength, duration) {
    const effect = {
      type: 'slow',
      strength: strength,  // 减速比例 (0-1)
      duration: duration,
      startTime: Date.now(),
      target: target
    }

    this.addEffect(target, effect)
  }

  /**
   * 添加中毒效果
   */
  addPoisonEffect(target, damage, duration) {
    const effect = {
      type: 'poison',
      damage: damage,  // 每秒伤害
      duration: duration,
      startTime: Date.now(),
      target: target,
      lastTick: Date.now()
    }

    this.addEffect(target, effect)
  }

  /**
   * 添加冰冻效果
   */
  addFreezeEffect(target, duration) {
    const effect = {
      type: 'freeze',
      duration: duration,
      startTime: Date.now(),
      target: target
    }

    this.addEffect(target, effect)
  }

  /**
   * 添加护甲削弱效果
   */
  addArmorReductionEffect(target, amount, duration) {
    const effect = {
      type: 'armor_reduction',
      amount: amount,
      duration: duration,
      startTime: Date.now(),
      target: target
    }

    this.addEffect(target, effect)
  }

  /**
   * 添加效果到目标
   */
  addEffect(target, effect) {
    if (!this.activeEffects.has(target.id)) {
      this.activeEffects.set(target.id, [])
    }

    const effects = this.activeEffects.get(target.id)

    // 检查是否已有同类型效果
    const existingIndex = effects.findIndex(e => e.type === effect.type)

    if (existingIndex >= 0) {
      // 刷新现有效果
      effects[existingIndex] = effect
    } else {
      // 添加新效果
      effects.push(effect)
    }

    // 立即应用效果
    this.applyEffect(target, effect)
  }

  /**
   * 更新所有效果
   */
  update(deltaTime) {
    const currentTime = Date.now()

    for (let [targetId, effects] of this.activeEffects) {
      // 更新效果并移除过期的
      for (let i = effects.length - 1; i >= 0; i--) {
        const effect = effects[i]

        if (currentTime - effect.startTime >= effect.duration) {
          // 效果过期，移除
          this.removeEffect(effect.target, effect)
          effects.splice(i, 1)
        } else {
          // 更新效果
          this.updateEffect(effect, deltaTime)
        }
      }

      // 如果目标没有效果了，移除目标
      if (effects.length === 0) {
        this.activeEffects.delete(targetId)
      }
    }
  }

  /**
   * 应用效果
   */
  applyEffect(target, effect) {
    switch (effect.type) {
      case 'slow':
        target.speedMultiplier = Math.min(target.speedMultiplier || 1, 1 - effect.strength)
        break

      case 'freeze':
        target.speedMultiplier = 0
        target.isFrozen = true
        break

      case 'armor_reduction':
        target.armorReduction = (target.armorReduction || 0) + effect.amount
        break
    }
  }

  /**
   * 更新单个效果
   */
  updateEffect(effect, deltaTime) {
    const currentTime = Date.now()

    switch (effect.type) {
      case 'poison':
        // 每秒造成伤害
        if (currentTime - effect.lastTick >= 1000) {
          effect.target.hp -= effect.damage
          effect.lastTick = currentTime

          // 检查死亡
          if (effect.target.hp <= 0) {
            effect.target.isAlive = false
          }
        }
        break
    }
  }

  /**
   * 移除效果
   */
  removeEffect(target, effect) {
    switch (effect.type) {
      case 'slow':
        // 重新计算速度倍数
        this.recalculateSpeedMultiplier(target)
        break

      case 'freeze':
        target.isFrozen = false
        this.recalculateSpeedMultiplier(target)
        break

      case 'armor_reduction':
        target.armorReduction = Math.max(0, (target.armorReduction || 0) - effect.amount)
        break
    }
  }

  /**
   * 重新计算速度倍数
   */
  recalculateSpeedMultiplier(target) {
    target.speedMultiplier = 1

    const effects = this.activeEffects.get(target.id) || []
    effects.forEach(effect => {
      if (effect.type === 'slow') {
        target.speedMultiplier = Math.min(target.speedMultiplier, 1 - effect.strength)
      } else if (effect.type === 'freeze') {
        target.speedMultiplier = 0
      }
    })
  }

  /**
   * 清理目标的所有效果
   */
  clearTargetEffects(target) {
    this.activeEffects.delete(target.id)

    // 重置目标状态
    target.speedMultiplier = 1
    target.isFrozen = false
    target.armorReduction = 0
  }

  /**
   * 获取目标的效果列表
   */
  getTargetEffects(target) {
    return this.activeEffects.get(target.id) || []
  }
}

// 导出模块
module.exports = { CombatSystem, EffectManager }
