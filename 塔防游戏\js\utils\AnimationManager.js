/**
 * 动画管理器
 * 管理游戏中的各种动画效果
 */

class AnimationManager {
  constructor() {
    // 动画队列
    this.animations = []
    this.nextId = 1
    
    // 缓动函数
    this.easingFunctions = {
      linear: t => t,
      easeInQuad: t => t * t,
      easeOutQuad: t => t * (2 - t),
      easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: t => t * t * t,
      easeOutCubic: t => (--t) * t * t + 1,
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
      easeInSine: t => 1 - Math.cos(t * Math.PI / 2),
      easeOutSine: t => Math.sin(t * Math.PI / 2),
      easeInOutSine: t => -(Math.cos(Math.PI * t) - 1) / 2,
      easeInBounce: t => 1 - this.easingFunctions.easeOutBounce(1 - t),
      easeOutBounce: t => {
        if (t < 1 / 2.75) {
          return 7.5625 * t * t
        } else if (t < 2 / 2.75) {
          return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75
        } else if (t < 2.5 / 2.75) {
          return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375
        } else {
          return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375
        }
      },
      easeInElastic: t => {
        if (t === 0) return 0
        if (t === 1) return 1
        const p = 0.3
        const s = p / 4
        return -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * (2 * Math.PI) / p))
      },
      easeOutElastic: t => {
        if (t === 0) return 0
        if (t === 1) return 1
        const p = 0.3
        const s = p / 4
        return Math.pow(2, -10 * t) * Math.sin((t - s) * (2 * Math.PI) / p) + 1
      }
    }
  }
  
  /**
   * 创建动画
   * @param {Object} config - 动画配置
   */
  createAnimation(config) {
    const animation = {
      id: this.nextId++,
      target: config.target,
      property: config.property,
      from: config.from,
      to: config.to,
      duration: config.duration || 1000,
      easing: config.easing || 'linear',
      delay: config.delay || 0,
      repeat: config.repeat || 0,
      yoyo: config.yoyo || false,
      onUpdate: config.onUpdate || null,
      onComplete: config.onComplete || null,
      
      // 内部状态
      startTime: Date.now() + (config.delay || 0),
      currentTime: 0,
      progress: 0,
      isPlaying: false,
      isCompleted: false,
      repeatCount: 0,
      direction: 1 // 1 for forward, -1 for reverse (yoyo)
    }
    
    this.animations.push(animation)
    return animation.id
  }
  
  /**
   * 创建多属性动画
   * @param {Object} config - 动画配置
   */
  createMultiPropertyAnimation(config) {
    const animations = []
    
    for (const property in config.properties) {
      const propertyConfig = {
        ...config,
        property: property,
        from: config.properties[property].from,
        to: config.properties[property].to
      }
      
      animations.push(this.createAnimation(propertyConfig))
    }
    
    return animations
  }
  
  /**
   * 创建序列动画
   * @param {Array} animationConfigs - 动画配置数组
   */
  createSequenceAnimation(animationConfigs) {
    const animations = []
    let totalDelay = 0
    
    animationConfigs.forEach(config => {
      const animConfig = {
        ...config,
        delay: (config.delay || 0) + totalDelay
      }
      
      animations.push(this.createAnimation(animConfig))
      totalDelay += (config.duration || 1000) + (config.delay || 0)
    })
    
    return animations
  }
  
  /**
   * 创建并行动画
   * @param {Array} animationConfigs - 动画配置数组
   */
  createParallelAnimation(animationConfigs) {
    const animations = []
    
    animationConfigs.forEach(config => {
      animations.push(this.createAnimation(config))
    })
    
    return animations
  }
  
  /**
   * 更新所有动画
   * @param {number} deltaTime - 时间增量
   */
  update(deltaTime) {
    const currentTime = Date.now()
    
    for (let i = this.animations.length - 1; i >= 0; i--) {
      const animation = this.animations[i]
      
      // 检查延迟
      if (currentTime < animation.startTime) {
        continue
      }
      
      if (!animation.isPlaying) {
        animation.isPlaying = true
      }
      
      // 计算进度
      animation.currentTime = currentTime - animation.startTime
      let progress = animation.currentTime / animation.duration
      
      // 处理yoyo效果
      if (animation.yoyo && animation.direction === -1) {
        progress = 1 - progress
      }
      
      // 限制进度范围
      progress = Math.max(0, Math.min(1, progress))
      
      // 应用缓动函数
      const easingFunc = this.easingFunctions[animation.easing] || this.easingFunctions.linear
      const easedProgress = easingFunc(progress)
      
      // 计算当前值
      const currentValue = this.interpolate(animation.from, animation.to, easedProgress)
      
      // 更新目标对象
      if (animation.target && animation.property) {
        this.setProperty(animation.target, animation.property, currentValue)
      }
      
      // 调用更新回调
      if (animation.onUpdate) {
        animation.onUpdate(currentValue, easedProgress, animation)
      }
      
      // 检查动画是否完成
      if (animation.currentTime >= animation.duration) {
        this.handleAnimationComplete(animation, i)
      }
    }
  }
  
  /**
   * 处理动画完成
   */
  handleAnimationComplete(animation, index) {
    // 处理重复
    if (animation.repeat > 0 || animation.repeat === -1) {
      animation.repeatCount++
      
      if (animation.repeat === -1 || animation.repeatCount < animation.repeat) {
        // 重置动画
        animation.startTime = Date.now()
        animation.currentTime = 0
        
        // 处理yoyo效果
        if (animation.yoyo) {
          animation.direction *= -1
          if (animation.direction === 1) {
            // 完成一个完整的yoyo循环
            animation.repeatCount++
          }
        }
        
        return
      }
    }
    
    // 动画完成
    animation.isCompleted = true
    
    // 调用完成回调
    if (animation.onComplete) {
      animation.onComplete(animation)
    }
    
    // 从动画列表中移除
    this.animations.splice(index, 1)
  }
  
  /**
   * 插值计算
   */
  interpolate(from, to, progress) {
    if (typeof from === 'number' && typeof to === 'number') {
      return from + (to - from) * progress
    }
    
    if (typeof from === 'object' && typeof to === 'object') {
      const result = {}
      for (const key in from) {
        if (to.hasOwnProperty(key)) {
          result[key] = this.interpolate(from[key], to[key], progress)
        }
      }
      return result
    }
    
    // 对于其他类型，在中点切换
    return progress < 0.5 ? from : to
  }
  
  /**
   * 设置对象属性
   */
  setProperty(target, property, value) {
    const keys = property.split('.')
    let obj = target
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!obj[keys[i]]) {
        obj[keys[i]] = {}
      }
      obj = obj[keys[i]]
    }
    
    obj[keys[keys.length - 1]] = value
  }
  
  /**
   * 停止动画
   * @param {number} animationId - 动画ID
   */
  stopAnimation(animationId) {
    const index = this.animations.findIndex(anim => anim.id === animationId)
    if (index >= 0) {
      this.animations.splice(index, 1)
    }
  }
  
  /**
   * 停止目标对象的所有动画
   * @param {Object} target - 目标对象
   */
  stopAnimationsForTarget(target) {
    for (let i = this.animations.length - 1; i >= 0; i--) {
      if (this.animations[i].target === target) {
        this.animations.splice(i, 1)
      }
    }
  }
  
  /**
   * 暂停所有动画
   */
  pauseAll() {
    this.animations.forEach(animation => {
      animation.isPaused = true
    })
  }
  
  /**
   * 恢复所有动画
   */
  resumeAll() {
    const currentTime = Date.now()
    this.animations.forEach(animation => {
      if (animation.isPaused) {
        animation.startTime += currentTime - animation.pauseTime
        animation.isPaused = false
      }
    })
  }
  
  /**
   * 清除所有动画
   */
  clear() {
    this.animations = []
  }
  
  /**
   * 获取动画数量
   */
  getAnimationCount() {
    return this.animations.length
  }
  
  /**
   * 预设动画效果
   */
  presets = {
    // 淡入
    fadeIn: (target, duration = 500) => {
      return this.createAnimation({
        target: target,
        property: 'alpha',
        from: 0,
        to: 1,
        duration: duration,
        easing: 'easeOutQuad'
      })
    },
    
    // 淡出
    fadeOut: (target, duration = 500) => {
      return this.createAnimation({
        target: target,
        property: 'alpha',
        from: 1,
        to: 0,
        duration: duration,
        easing: 'easeInQuad'
      })
    },
    
    // 缩放进入
    scaleIn: (target, duration = 300) => {
      return this.createAnimation({
        target: target,
        property: 'scale',
        from: 0,
        to: 1,
        duration: duration,
        easing: 'easeOutBounce'
      })
    },
    
    // 缩放退出
    scaleOut: (target, duration = 300) => {
      return this.createAnimation({
        target: target,
        property: 'scale',
        from: 1,
        to: 0,
        duration: duration,
        easing: 'easeInQuad'
      })
    },
    
    // 滑入
    slideIn: (target, direction = 'left', duration = 400) => {
      const fromX = direction === 'left' ? -100 : (direction === 'right' ? 100 : 0)
      const fromY = direction === 'up' ? -100 : (direction === 'down' ? 100 : 0)
      
      return this.createMultiPropertyAnimation({
        target: target,
        duration: duration,
        easing: 'easeOutQuad',
        properties: {
          x: { from: target.x + fromX, to: target.x },
          y: { from: target.y + fromY, to: target.y }
        }
      })
    },
    
    // 弹跳
    bounce: (target, intensity = 20, duration = 600) => {
      return this.createAnimation({
        target: target,
        property: 'y',
        from: target.y,
        to: target.y - intensity,
        duration: duration,
        easing: 'easeOutBounce',
        yoyo: true
      })
    },
    
    // 摇摆
    shake: (target, intensity = 10, duration = 500) => {
      const originalX = target.x
      return this.createAnimation({
        target: target,
        property: 'x',
        from: originalX - intensity,
        to: originalX + intensity,
        duration: duration / 4,
        repeat: 4,
        yoyo: true,
        onComplete: () => {
          target.x = originalX
        }
      })
    },
    
    // 脉冲
    pulse: (target, scale = 1.2, duration = 800) => {
      return this.createAnimation({
        target: target,
        property: 'scale',
        from: 1,
        to: scale,
        duration: duration,
        easing: 'easeInOutSine',
        repeat: -1,
        yoyo: true
      })
    }
  }
}

// 创建全局动画管理器实例
const animationManager = new AnimationManager()

// 导出模块
module.exports = animationManager
