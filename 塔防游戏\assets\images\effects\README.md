# 特效图片资源

## 图片文件

请将以下特效图片放入此目录：

- `rotation_effect.png` - 正向旋转特效图片
- `rotation_effect_reverse.png` - 反向旋转特效图片
- `fade_effect.png` - 若隐若现特效图片

## 序列帧动画图片

### 稀有品质序列帧 (蓝色)
- `rare_frame_01.png` ~ `rare_frame_10.png` - 稀有品质序列帧动画 (10张)

### 史诗品质序列帧 (紫色)
- `epic_frame_01.png` ~ `epic_frame_10.png` - 史诗品质序列帧动画 (10张)

### 传说品质序列帧 (橙金色)
- `legendary_frame_01.png` ~ `legendary_frame_10.png` - 传说品质序列帧动画 (10张)

### 返回按钮序列帧
- `back_button_01.png` ~ `back_button_08.png` - 返回按钮序列帧动画 (8张)

## 图片要求

- **尺寸**:
  - 旋转特效：建议120x120px (正方形，1:1比例)
  - 若隐若现特效：建议162x288px (垂直长方形，9:16比例)
- **格式**: PNG (支持透明度)
- **设计**: 圆形或星形图案，适合旋转动画
- **背景**: 透明背景
- **颜色**: 可以是金色、白色或其他装饰色

## 使用说明

旋转图片会显示在每个英雄卡片的后面（背景层），两张图片分别正向和反向旋转，营造丰富的动态效果。

## 三层特效系统

- **若隐若现层**：fade_effect.png 透明度渐变动画（最底层）
- **反向旋转层**：rotation_effect_reverse.png 反向旋转（逆时针）
- **正向旋转层**：rotation_effect.png 正向旋转（顺时针）
- **层级关系**：若隐若现 → 反向旋转 → 正向旋转

## 设计建议

### 旋转特效图片
- **圆形图案**: 如光环、魔法阵、齿轮等
- **对称设计**: 旋转时视觉效果更好
- **适度透明**: 不要太抢眼，作为背景装饰
- **细节丰富**: 旋转时能看到细节变化

### 若隐若现特效图片
- **垂直长方形**: 162x288px，9:16比例，与卡片比例一致
- **神秘图案**: 如光柱、能量流、魔法光束等
- **渐变设计**: 上下渐变或中心向外渐变效果更佳
- **适中对比度**: 透明度范围0.1-0.4，确保若隐若现效果明显
- **明显变化**: 设计时考虑透明度变化的视觉效果

### 序列帧动画图片
- **尺寸**: 169x112像素 (比例约1.51:1)
- **格式**: PNG (支持透明度)
- **帧数**: 每组10张图片 (frame_01 到 frame_10)
- **命名**: 使用两位数字编号 (01, 02, 03, ..., 10)
- **动画时长**: 建议每帧显示100-150ms，总时长约1.0-1.5秒
- **循环播放**: 设计时考虑第10帧能自然过渡到第1帧
- **显示方式**: 保持原始比例，在卡片内容区居中显示

### 品质序列帧设计建议

**稀有品质 (蓝色)**:
- **主色调**: 蓝色系 (#4A90E2)
- **效果**: 清新的蓝色光效、水波纹、冰晶效果等
- **强度**: 适中，不要过于抢眼

**史诗品质 (紫色)**:
- **主色调**: 紫色系 (#8E44AD)
- **效果**: 神秘的紫色魔法、星空效果、能量波动等
- **强度**: 较强，体现稀有性

**传说品质 (橙金色)**:
- **主色调**: 橙金色系 (#E67E22)
- **效果**: 华丽的金色光芒、火焰效果、神圣光环等
- **强度**: 最强，极其华丽的视觉效果

### 返回按钮序列帧设计建议

**返回按钮序列帧 (8张)**:
- **尺寸**: 80x36像素 (更长的长方形按钮尺寸)
- **主色调**: 白色或浅色系，确保在各种背景下可见
- **效果**: 简洁的箭头动画、光效闪烁、按钮呼吸效果等
- **设计**: 保持箭头"←"的基本形状，添加动态效果
- **形状**: 长方形设计，圆角半径约8px
- **循环**: 第8帧要能自然过渡到第1帧
- **透明背景**: 使用PNG格式，背景透明
