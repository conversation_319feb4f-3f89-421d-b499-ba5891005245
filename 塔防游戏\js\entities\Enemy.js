/**
 * 敌人基类
 * 所有敌人的基类
 */
const BaseEntity = require('./BaseEntity.js')
const EnemyConfig = require('../config/EnemyConfig.js')

class Enemy extends BaseEntity {
  /**
   * 构造函数
   * @param {Array} path - 移动路径
   * @param {string} enemyId - 敌人类型ID
   * @param {number} difficultyMultiplier - 难度倍数
   */
  constructor(path, enemyId, difficultyMultiplier = 1) {
    // 从路径起点开始
    const startPos = path && path.length > 0 ? path[0] : { x: 0, y: 0 }
    super(startPos.x, startPos.y)
    
    // 基本属性
    this.type = 'enemy'
    this.enemyId = enemyId
    this.path = path || []
    this.pathIndex = 0
    this.pathProgress = 0  // 在当前路径段的进度 0-1
    
    // 加载敌人配置
    this.loadConfig(difficultyMultiplier)
    
    // 移动相关
    this.velocity = { x: 0, y: 0 }
    this.baseSpeed = this.speed
    this.speedMultiplier = 1  // 速度倍数（用于减速效果）
    
    // 状态效果
    this.effects = []
    this.isFrozen = false
    this.armorReduction = 0
    
    // 动画相关
    this.animationFrame = 0
    this.animationTime = 0
    this.deathAnimation = 0
    this.hitAnimation = 0
    
    // 计算初始方向
    this.updateDirection()
  }
  
  /**
   * 加载敌人配置
   */
  loadConfig(difficultyMultiplier = 1) {
    const config = EnemyConfig.getScaledEnemyStats(this.enemyId, difficultyMultiplier)
    if (!config) {
      console.error(`找不到敌人配置: ${this.enemyId}`)
      return
    }
    
    // 从配置中加载属性
    this.maxHp = config.baseStats.hp
    this.hp = this.maxHp
    this.speed = config.baseStats.speed
    this.armor = config.baseStats.armor
    this.magicResist = config.baseStats.magicResist
    this.reward = config.baseStats.reward
    this.exp = config.baseStats.exp
    this.damage = config.baseStats.damage  // 到达终点时造成的伤害
    
    // 加载抗性
    this.resistances = { ...config.resistances }
    
    // 加载视觉配置
    if (config.visual) {
      this.color = config.visual.color || '#8B4513'
      this.size = config.visual.size || 24
      this.animationFrames = config.visual.animationFrames || 4
      this.animationSpeed = config.visual.animationSpeed || 200
    }
    
    // 加载移动配置
    if (config.movement) {
      this.canFly = config.movement.canFly || false
      this.canSwim = config.movement.canSwim || false
    }
    
    // 加载能力
    this.abilities = config.abilities || []
    
    // 更新大小
    this.setSize(this.size, this.size)
  }
  
  /**
   * 更新敌人状态
   */
  update(deltaTime) {
    super.update(deltaTime)
    
    // 如果死亡，播放死亡动画
    if (!this.isAlive) {
      this.updateDeathAnimation(deltaTime)
      return
    }
    
    // 更新动画
    this.updateAnimation(deltaTime)
    
    // 更新受击动画
    if (this.hitAnimation > 0) {
      this.hitAnimation -= deltaTime / 200  // 受击动画持续0.2秒
      if (this.hitAnimation < 0) {
        this.hitAnimation = 0
      }
    }
    
    // 如果被冰冻，不移动
    if (this.isFrozen) {
      return
    }
    
    // 移动
    this.move(deltaTime)
  }
  
  /**
   * 移动逻辑
   */
  move(deltaTime) {
    if (!this.path || this.path.length < 2) {
      return
    }
    
    // 计算实际移动速度
    const actualSpeed = this.speed * this.speedMultiplier
    const moveDistance = actualSpeed * deltaTime / 1000
    
    // 获取当前目标点
    const currentTarget = this.path[this.pathIndex + 1]
    if (!currentTarget) {
      // 到达终点
      this.reachEnd()
      return
    }
    
    // 计算到目标点的距离和方向
    const dx = currentTarget.x - this.x
    const dy = currentTarget.y - this.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    if (distance <= moveDistance) {
      // 到达当前路径点
      this.x = currentTarget.x
      this.y = currentTarget.y
      this.pathIndex++
      this.pathProgress = 0
      
      // 更新方向
      this.updateDirection()
    } else {
      // 向目标点移动
      const moveX = (dx / distance) * moveDistance
      const moveY = (dy / distance) * moveDistance
      
      this.x += moveX
      this.y += moveY
      
      // 更新路径进度
      this.pathProgress = 1 - (distance - moveDistance) / distance
      
      // 更新速度向量（用于渲染）
      this.velocity.x = moveX / deltaTime * 1000
      this.velocity.y = moveY / deltaTime * 1000
    }
  }
  
  /**
   * 更新移动方向
   */
  updateDirection() {
    if (!this.path || this.pathIndex + 1 >= this.path.length) {
      return
    }
    
    const currentTarget = this.path[this.pathIndex + 1]
    this.lookAtPoint(currentTarget.x, currentTarget.y)
  }
  
  /**
   * 到达终点
   */
  reachEnd() {
    if (this.onReachEnd) {
      this.onReachEnd(this)
    }
    this.destroy()
  }
  
  /**
   * 受到伤害
   * @param {number} damage - 伤害值
   * @param {string} damageType - 伤害类型
   */
  takeDamage(damage, damageType = 'physical') {
    // 计算实际伤害
    let actualDamage = damage
    
    // 应用护甲和抗性
    if (damageType === 'physical') {
      const effectiveArmor = Math.max(0, this.armor - this.armorReduction)
      actualDamage = Math.max(1, damage - effectiveArmor)
      
      // 应用物理抗性
      if (this.resistances.physical > 0) {
        actualDamage *= (1 - this.resistances.physical)
      }
    } else if (damageType === 'magic') {
      actualDamage = damage * (1 - this.magicResist / 100)
      
      // 应用魔法抗性
      if (this.resistances.magic > 0) {
        actualDamage *= (1 - this.resistances.magic)
      }
    }
    
    // 确保最小伤害
    actualDamage = Math.max(1, Math.floor(actualDamage))
    
    // 扣除生命值
    this.hp -= actualDamage
    
    // 触发受击动画
    this.hitAnimation = 1
    
    // 检查死亡
    if (this.hp <= 0) {
      this.hp = 0
      this.die()
    }
    
    return actualDamage
  }
  
  /**
   * 死亡处理
   */
  die() {
    this.isAlive = false
    this.deathAnimation = 1
    
    // 触发死亡事件
    if (this.onDeath) {
      this.onDeath(this)
    }
  }
  
  /**
   * 应用效果
   * @param {Object} effect - 效果对象
   */
  applyEffect(effect) {
    switch (effect.type) {
      case 'slow':
        this.speedMultiplier = Math.min(this.speedMultiplier, 1 - effect.strength)
        break
        
      case 'freeze':
        this.isFrozen = true
        this.speedMultiplier = 0
        break
        
      case 'poison':
        // 中毒效果由外部系统处理
        break
        
      case 'armor_reduction':
        this.armorReduction += effect.amount
        break
    }
  }
  
  /**
   * 移除效果
   * @param {Object} effect - 效果对象
   */
  removeEffect(effect) {
    switch (effect.type) {
      case 'slow':
        // 重新计算速度倍数
        this.recalculateSpeedMultiplier()
        break
        
      case 'freeze':
        this.isFrozen = false
        this.recalculateSpeedMultiplier()
        break
        
      case 'armor_reduction':
        this.armorReduction = Math.max(0, this.armorReduction - effect.amount)
        break
    }
  }
  
  /**
   * 重新计算速度倍数
   */
  recalculateSpeedMultiplier() {
    this.speedMultiplier = 1
    
    // 这里应该由效果管理器来处理
    // 暂时简化处理
    if (this.isFrozen) {
      this.speedMultiplier = 0
    }
  }
  
  /**
   * 更新动画
   */
  updateAnimation(deltaTime) {
    this.animationTime += deltaTime
    
    if (this.animationTime >= this.animationSpeed) {
      this.animationFrame = (this.animationFrame + 1) % this.animationFrames
      this.animationTime = 0
    }
  }
  
  /**
   * 更新死亡动画
   */
  updateDeathAnimation(deltaTime) {
    if (this.deathAnimation > 0) {
      this.deathAnimation -= deltaTime / 500  // 死亡动画持续0.5秒
      if (this.deathAnimation <= 0) {
        this.deathAnimation = 0
        this.isVisible = false  // 动画结束后隐藏
      }
    }
  }
  
  /**
   * 绘制敌人
   */
  draw(ctx) {
    // 如果死亡动画结束，不绘制
    if (!this.isAlive && this.deathAnimation <= 0) {
      return
    }
    
    // 死亡动画效果
    if (!this.isAlive) {
      ctx.globalAlpha = this.deathAnimation
      ctx.scale(1 + (1 - this.deathAnimation) * 0.5, 1 + (1 - this.deathAnimation) * 0.5)
    }
    
    // 受击动画效果
    if (this.hitAnimation > 0) {
      ctx.fillStyle = '#ffffff'
      ctx.globalAlpha = this.hitAnimation * 0.5
      ctx.beginPath()
      ctx.arc(0, 0, this.width / 2 + 5, 0, Math.PI * 2)
      ctx.fill()
      ctx.globalAlpha = 1
    }
    
    // 绘制敌人主体
    this.drawBody(ctx)
    
    // 绘制生命条
    if (this.isAlive && this.hp < this.maxHp) {
      this.drawHealthBar(ctx)
    }
    
    // 绘制状态效果
    this.drawStatusEffects(ctx)
    
    // 恢复透明度
    if (!this.isAlive) {
      ctx.globalAlpha = 1
    }
  }
  
  /**
   * 绘制敌人主体
   */
  drawBody(ctx) {
    // 绘制敌人身体
    ctx.fillStyle = this.color || '#8B4513'
    
    // 如果被冰冻，添加蓝色调
    if (this.isFrozen) {
      ctx.fillStyle = '#87CEEB'
    }
    
    ctx.beginPath()
    ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2)
    ctx.fill()
    
    // 绘制方向指示器
    ctx.fillStyle = '#ffffff'
    ctx.beginPath()
    ctx.arc(this.width / 4, 0, 2, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制生命条
   */
  drawHealthBar(ctx) {
    const barWidth = this.width * 1.2
    const barHeight = 4
    const barY = -this.height / 2 - 10
    
    // 背景
    ctx.fillStyle = '#333333'
    ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight)
    
    // 生命值
    const healthPercent = this.hp / this.maxHp
    ctx.fillStyle = healthPercent > 0.5 ? '#00ff00' : (healthPercent > 0.25 ? '#ffff00' : '#ff0000')
    ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight)
  }
  
  /**
   * 绘制状态效果
   */
  drawStatusEffects(ctx) {
    let iconX = -this.width / 2
    const iconY = this.height / 2 + 5
    const iconSize = 8
    
    // 减速效果
    if (this.speedMultiplier < 1 && !this.isFrozen) {
      ctx.fillStyle = '#ffff00'
      ctx.fillRect(iconX, iconY, iconSize, iconSize)
      iconX += iconSize + 2
    }
    
    // 冰冻效果
    if (this.isFrozen) {
      ctx.fillStyle = '#87CEEB'
      ctx.fillRect(iconX, iconY, iconSize, iconSize)
      iconX += iconSize + 2
    }
    
    // 护甲削弱效果
    if (this.armorReduction > 0) {
      ctx.fillStyle = '#9ACD32'
      ctx.fillRect(iconX, iconY, iconSize, iconSize)
      iconX += iconSize + 2
    }
  }
  
  /**
   * 获取敌人信息
   */
  getEnemyInfo() {
    const enemyConfig = EnemyConfig.getEnemyConfig(this.enemyId)
    
    return {
      id: this.id,
      type: this.type,
      enemyId: this.enemyId,
      name: enemyConfig ? enemyConfig.name : '未知敌人',
      hp: this.hp,
      maxHp: this.maxHp,
      armor: this.armor,
      speed: this.speed,
      reward: this.reward,
      position: { x: this.x, y: this.y },
      pathProgress: this.pathIndex + this.pathProgress,
      effects: this.effects
    }
  }
}

// 导出模块
module.exports = Enemy
