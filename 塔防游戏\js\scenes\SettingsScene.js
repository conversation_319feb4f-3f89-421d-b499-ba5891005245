/**
 * 设置场景
 * 游戏设置界面
 */
const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')
const DataManager = require('../utils/DataManager.js')
const AudioManager = require('../utils/AudioManager.js')

class SettingsScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 创建返回按钮
    this.backButtonComponent = new BackButton(require('../utils/ImageManager.js'))
    
    // 设置数据
    this.settings = DataManager.getSettings()
    
    // UI元素
    this.settingItems = []
    this.selectedItem = -1
    
    // 初始化UI
    this.initializeUI()
    
    // 绑定事件
    this.bindEvents()
  }
  
  /**
   * 初始化UI
   */
  initializeUI() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()
    
    // 设置项配置
    this.settingItems = [
      {
        id: 'musicEnabled',
        name: '背景音乐',
        type: 'toggle',
        value: this.settings.musicEnabled,
        y: 150
      },
      {
        id: 'sfxEnabled',
        name: '音效',
        type: 'toggle',
        value: this.settings.sfxEnabled,
        y: 200
      },
      {
        id: 'musicVolume',
        name: '音乐音量',
        type: 'slider',
        value: this.settings.musicVolume,
        min: 0,
        max: 1,
        step: 0.1,
        y: 250
      },
      {
        id: 'sfxVolume',
        name: '音效音量',
        type: 'slider',
        value: this.settings.sfxVolume,
        min: 0,
        max: 1,
        step: 0.1,
        y: 300
      },
      {
        id: 'showFPS',
        name: '显示FPS',
        type: 'toggle',
        value: this.settings.showFPS,
        y: 350
      },
      {
        id: 'autoSave',
        name: '自动保存',
        type: 'toggle',
        value: this.settings.autoSave,
        y: 400
      }
    ]
    
    // 计算位置
    this.settingItems.forEach(item => {
      item.x = windowWidth / 2 - 150
      item.width = 300
      item.height = 40
    })
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    
    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
      return
    }
    
    // 检查设置项
    this.selectedItem = -1
    for (let i = 0; i < this.settingItems.length; i++) {
      const item = this.settingItems[i]
      if (this.isPointInRect(pos, item)) {
        this.selectedItem = i
        break
      }
    }
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查返回按钮
    if (this.backButtonComponent.isPressed() && this.backButtonComponent.isPointInButton(pos)) {
      this.onBackClick()
      this.backButtonComponent.setPressed(false)
      return
    }
    this.backButtonComponent.setPressed(false)
    
    // 处理设置项点击
    if (this.selectedItem >= 0) {
      const item = this.settingItems[this.selectedItem]
      if (this.isPointInRect(pos, item)) {
        this.handleSettingClick(item, pos)
      }
    }
    
    this.selectedItem = -1
  }
  
  /**
   * 处理设置项点击
   */
  handleSettingClick(item, pos) {
    switch (item.type) {
      case 'toggle':
        this.toggleSetting(item)
        break
      case 'slider':
        this.adjustSlider(item, pos)
        break
    }
  }
  
  /**
   * 切换开关设置
   */
  toggleSetting(item) {
    item.value = !item.value
    this.settings[item.id] = item.value
    
    // 立即应用设置
    this.applySetting(item.id, item.value)
    
    // 保存设置
    DataManager.updateSettings({ [item.id]: item.value })
    
    // 播放点击音效
    AudioManager.playSfx('button_click', { volume: 0.6 })
  }
  
  /**
   * 调整滑块设置
   */
  adjustSlider(item, pos) {
    const sliderX = item.x + 150
    const sliderWidth = 120
    const relativeX = pos.x - sliderX
    const percent = Math.max(0, Math.min(1, relativeX / sliderWidth))
    
    item.value = item.min + (item.max - item.min) * percent
    item.value = Math.round(item.value / item.step) * item.step
    item.value = Math.max(item.min, Math.min(item.max, item.value))
    
    this.settings[item.id] = item.value
    
    // 立即应用设置
    this.applySetting(item.id, item.value)
    
    // 保存设置
    DataManager.updateSettings({ [item.id]: item.value })
    
    // 播放点击音效
    AudioManager.playSfx('button_click', { volume: 0.4 })
  }
  
  /**
   * 应用设置
   */
  applySetting(settingId, value) {
    switch (settingId) {
      case 'musicEnabled':
        AudioManager.setMusicEnabled(value)
        if (!value) {
          AudioManager.stopMusic()
        }
        break
        
      case 'sfxEnabled':
        AudioManager.setSfxEnabled(value)
        break
        
      case 'musicVolume':
        AudioManager.setMusicVolume(value)
        break
        
      case 'sfxVolume':
        AudioManager.setSfxVolume(value)
        break
    }
  }
  
  /**
   * 检查点是否在矩形内
   */
  isPointInRect(point, rect) {
    return point.x >= rect.x && 
           point.x <= rect.x + rect.width &&
           point.y >= rect.y && 
           point.y <= rect.y + rect.height
  }
  
  /**
   * 返回按钮点击
   */
  onBackClick() {
    if (this.onSceneChange) {
      this.onSceneChange('login')
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    // 设置场景通常不需要更新逻辑
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 清空画布
    this.ctx.clearRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
    
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())
    
    // 绘制背景
    this.drawBackground()
    
    // 绘制标题
    this.drawTitle()
    
    // 绘制设置项
    this.drawSettingItems()
    
    // 绘制返回按钮
    this.backButtonComponent.draw(this.ctx)
    
    this.ctx.restore()
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.adapter.getWindowHeight())
    gradient.addColorStop(0, '#1e3c72')
    gradient.addColorStop(1, '#2a5298')
    
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
  }
  
  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 32px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('游戏设置', this.adapter.getWindowWidth() / 2, 80)
  }
  
  /**
   * 绘制设置项
   */
  drawSettingItems() {
    this.settingItems.forEach((item, index) => {
      this.drawSettingItem(item, index === this.selectedItem)
    })
  }
  
  /**
   * 绘制单个设置项
   */
  drawSettingItem(item, isSelected) {
    // 背景
    this.ctx.fillStyle = isSelected ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)'
    this.ctx.fillRect(item.x, item.y, item.width, item.height)
    
    // 边框
    this.ctx.strokeStyle = isSelected ? '#ffffff' : 'rgba(255, 255, 255, 0.3)'
    this.ctx.lineWidth = 1
    this.ctx.strokeRect(item.x, item.y, item.width, item.height)
    
    // 设置名称
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '16px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(item.name, item.x + 20, item.y + item.height / 2)
    
    // 绘制控件
    if (item.type === 'toggle') {
      this.drawToggle(item)
    } else if (item.type === 'slider') {
      this.drawSlider(item)
    }
  }
  
  /**
   * 绘制开关
   */
  drawToggle(item) {
    const toggleX = item.x + item.width - 80
    const toggleY = item.y + 10
    const toggleWidth = 60
    const toggleHeight = 20
    
    // 开关背景
    this.ctx.fillStyle = item.value ? '#4CAF50' : '#757575'
    this.ctx.fillRect(toggleX, toggleY, toggleWidth, toggleHeight)
    
    // 开关滑块
    const sliderX = item.value ? toggleX + toggleWidth - 18 : toggleX + 2
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(sliderX, toggleY + 2, 16, 16)
  }
  
  /**
   * 绘制滑块
   */
  drawSlider(item) {
    const sliderX = item.x + 150
    const sliderY = item.y + item.height / 2 - 2
    const sliderWidth = 120
    const sliderHeight = 4
    
    // 滑块轨道
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
    this.ctx.fillRect(sliderX, sliderY, sliderWidth, sliderHeight)
    
    // 滑块进度
    const progress = (item.value - item.min) / (item.max - item.min)
    this.ctx.fillStyle = '#4CAF50'
    this.ctx.fillRect(sliderX, sliderY, sliderWidth * progress, sliderHeight)
    
    // 滑块手柄
    const handleX = sliderX + sliderWidth * progress - 6
    const handleY = sliderY - 4
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(handleX, handleY, 12, 12)
    
    // 数值显示
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'right'
    this.ctx.fillText(Math.round(item.value * 100) + '%', item.x + item.width - 20, item.y + item.height / 2)
  }
  
  /**
   * 销毁场景
   */
  destroy() {
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }
    
    this.touchStartHandler = null
    this.touchEndHandler = null
  }
}

// 导出模块
module.exports = SettingsScene
