const imageManager = require('./ImageManager.js')

// 星级显示配置
const STAR_SIZE = 24    // 星星图片大小（增大到24px）
const STAR_SPACING = 2  // 星星间距（增加间距）

/**
 * 星级渲染器
 * 负责使用图片资源绘制星级
 */
class StarRenderer {
  constructor() {
    this.starImages = {}
    this.isLoaded = false
  }
  
  /**
   * 初始化并预加载星级图片
   * @returns {Promise<boolean>} 是否加载成功
   */
  async initialize() {
    try {
      this.starImages = await imageManager.preloadAllStarImages()
      this.isLoaded = true
      return true
    } catch (error) {
      console.error('星级图片预加载失败:', error)
      this.isLoaded = false
      return false
    }
  }
  
  /**
   * 检查是否准备就绪
   */
  isReady() {
    return this.isLoaded
  }
  
  /**
   * 绘制星级
   * @param {CanvasRenderingContext2D} ctx Canvas上下文
   * @param {number} x 中心X坐标
   * @param {number} y 中心Y坐标
   * @param {number} starLevel 星级 (1-15)
   */
  drawStars(ctx, x, y, starLevel) {
    if (!this.isLoaded) {
      // 如果图片未加载，回退到文字绘制
      this.drawFallbackStars(ctx, x, y, starLevel)
      return
    }
    
    const { color, count } = this.getStarConfig(starLevel)
    const starImage = this.starImages[color]
    
    if (!starImage) {
      this.drawFallbackStars(ctx, x, y, starLevel)
      return
    }
    
    ctx.save()
    
    // 绘制星级图片
    const totalWidth = count * STAR_SIZE + (count - 1) * STAR_SPACING
    const startX = x - totalWidth / 2

    for (let i = 0; i < count; i++) {
      const starX = startX + i * (STAR_SIZE + STAR_SPACING)
      const starY = y - STAR_SIZE / 2

      ctx.drawImage(starImage, starX, starY, STAR_SIZE, STAR_SIZE)
    }
    
    ctx.restore()
  }
  
  /**
   * 获取星级配置
   * @param {number} starLevel 星级 (1-15)
   * @returns {Object} 星级配置
   */
  getStarConfig(starLevel) {
    if (starLevel >= 1 && starLevel <= 5) {
      return { color: 'purple', count: starLevel }
    } else if (starLevel >= 6 && starLevel <= 10) {
      return { color: 'yellow', count: starLevel - 5 } // 6星=1个黄星，10星=5个黄星
    } else if (starLevel >= 11 && starLevel <= 15) {
      return { color: 'red', count: starLevel - 10 }    // 11星=1个红星，15星=5个红星
    } else {
      // 默认1星
      return { color: 'purple', count: 1 }
    }
  }
  
  /**
   * 回退到文字绘制星级
   */
  drawFallbackStars(ctx, x, y, starLevel) {
    const { color, count } = this.getStarConfig(starLevel)
    
    // 颜色映射
    const colors = {
      purple: '#8B5CF6',
      yellow: '#F59E0B',
      red: '#EF4444'
    }
    
    ctx.save()
    ctx.fillStyle = colors[color]
    ctx.font = '22px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    const stars = '★'.repeat(count)
    ctx.fillText(stars, x, y)
    
    ctx.restore()
  }
}

// 创建单例
const starRenderer = new StarRenderer()

module.exports = starRenderer
