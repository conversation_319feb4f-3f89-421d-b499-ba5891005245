# 🏰 塔系统测试指南

## 🎯 测试目标

验证塔的建造、升级、出售功能是否正常工作。

## 🚀 快速测试步骤

### 1. 启动游戏
1. 在微信开发者工具中编译运行
2. 登录 → 挑战 → 冒险关卡 → 关卡1 → 开始挑战

### 2. 测试建造功能

#### 🎮 **简化测试模式** (TestBattleScene)
- **操作**: 点击空地任意位置
- **预期**: 自动建造一个箭塔（消耗50金币）
- **验证**: 
  - ✅ 塔出现在点击位置
  - ✅ 金币减少50
  - ✅ 塔开始攻击敌人

#### 🏰 **完整战斗模式** (BattleScene)
- **操作**: 点击空地
- **预期**: 显示建造菜单
- **验证**:
  - ✅ 弹出建造菜单
  - ✅ 显示5种塔类型
  - ✅ 显示塔的名称、图标、费用
  - ✅ 点击塔类型可以选择

### 3. 测试塔选择和信息显示

#### 📊 **塔信息面板**
- **操作**: 点击已建造的塔
- **预期**: 显示塔信息面板
- **验证**:
  - ✅ 显示塔的等级、伤害、射程、攻速
  - ✅ 显示特殊属性（减速、毒伤、范围等）
  - ✅ 显示升级费用
  - ✅ 显示出售价值

### 4. 测试升级功能

#### ⬆️ **塔升级**
- **操作**: 
  1. 点击塔打开信息面板
  2. 点击"升级"按钮
- **预期**: 塔升级成功
- **验证**:
  - ✅ 等级从1升到2
  - ✅ 伤害、射程等属性提升
  - ✅ 金币减少（升级费用）
  - ✅ 升级特效播放
  - ✅ 升级音效播放

#### 🚫 **升级限制**
- **测试场景**: 金币不足或已满级
- **验证**:
  - ✅ 金币不足时按钮显示"金币不足"（橙色）
  - ✅ 满级时按钮显示"满级"（灰色）
  - ✅ 无法点击升级

### 5. 测试出售功能

#### 💰 **塔出售**
- **操作**:
  1. 点击塔打开信息面板
  2. 点击"出售"按钮
  3. 确认出售
- **预期**: 塔被出售
- **验证**:
  - ✅ 显示确认对话框
  - ✅ 塔消失
  - ✅ 获得出售金币
  - ✅ 位置变为可建造

## 🎮 详细测试流程

### 测试流程A：简化模式
```
1. 启动游戏 → TestBattleScene
2. 点击空地 → 建造塔（自动）
3. 观察塔攻击敌人
4. 重复建造多个塔
5. 验证金币消耗
```

### 测试流程B：完整模式
```
1. 启动游戏 → BattleScene
2. 点击空地 → 建造菜单
3. 选择箭塔 → 建造成功
4. 点击塔 → 信息面板
5. 升级塔 → 验证属性提升
6. 继续升级到满级
7. 出售塔 → 验证金币回收
```

## 🔍 预期行为

### ✅ 正常功能
- **建造菜单**: 点击空地显示塔选择
- **塔信息**: 点击塔显示详细信息
- **升级系统**: 3级升级，属性递增
- **出售系统**: 回收部分建造成本
- **UI反馈**: 按钮状态根据条件变化
- **音效**: 建造、升级音效播放
- **特效**: 建造、升级特效显示

### 🎨 视觉效果
- **建造菜单**: 黑色半透明背景，白色边框
- **塔信息面板**: 详细属性显示，彩色特殊属性
- **升级按钮**: 绿色（可升级）、橙色（金币不足）、灰色（满级）
- **出售按钮**: 红色背景，显示出售价值
- **特效**: 建造和升级时的光效

### 🎵 音频效果
- **建造音效**: build_tower.mp3
- **升级音效**: tower_upgrade.mp3
- **出售音效**: tower_sell.mp3
- **按钮音效**: button_click.mp3

## 🐛 常见问题排查

### Q: 点击空地没有反应？
**可能原因**:
- 点击位置不是可建造区域
- UI事件处理有问题
- 游戏暂停状态

**解决方案**:
- 确保点击草地区域（绿色）
- 避免点击路径（棕色）
- 检查游戏是否暂停

### Q: 建造菜单不显示？
**可能原因**:
- BattleUI初始化问题
- 塔数据配置错误
- 事件处理逻辑错误

**解决方案**:
- 检查控制台错误信息
- 验证TowerConfig.js配置
- 使用TestBattleScene测试基础功能

### Q: 升级按钮无效？
**可能原因**:
- 金币不足
- 塔已满级
- 升级费用计算错误

**解决方案**:
- 检查金币数量
- 确认塔等级
- 查看控制台错误

### Q: 塔信息显示错误？
**可能原因**:
- 塔属性未正确初始化
- UI渲染逻辑错误
- 数据同步问题

**解决方案**:
- 检查Tower.js构造函数
- 验证属性计算逻辑
- 确认UI数据绑定

## 📊 测试检查清单

### 基础功能
- [ ] 游戏正常启动
- [ ] 场景切换正常
- [ ] 触摸事件响应
- [ ] UI界面显示

### 建造系统
- [ ] 点击空地显示建造菜单
- [ ] 塔类型选择正常
- [ ] 建造成功，塔出现
- [ ] 金币正确扣除
- [ ] 建造音效播放

### 升级系统
- [ ] 点击塔显示信息面板
- [ ] 属性信息正确显示
- [ ] 升级按钮状态正确
- [ ] 升级成功，属性提升
- [ ] 升级费用正确扣除
- [ ] 满级状态正确显示

### 出售系统
- [ ] 出售按钮显示价值
- [ ] 确认对话框正常
- [ ] 出售成功，塔消失
- [ ] 金币正确增加
- [ ] 位置重新可建造

### 视觉效果
- [ ] UI界面美观
- [ ] 按钮状态变化
- [ ] 特效正常播放
- [ ] 颜色编码正确

### 音频效果
- [ ] 建造音效
- [ ] 升级音效
- [ ] 按钮音效
- [ ] 音量控制正常

## 🎉 测试完成

如果所有功能都正常工作，恭喜！您的塔防游戏塔系统已经完全可用。

如果遇到问题，请参考：
- `troubleshooting_guide.md` - 详细问题排查
- `QUICK_START_GUIDE.md` - 快速启动指南
- 控制台输出 - 实时错误信息

**享受建造和升级塔的乐趣吧！** 🏰✨
