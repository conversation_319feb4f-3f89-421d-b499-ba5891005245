/**
 * 敌人配置系统
 * 定义所有敌人类型的属性和行为
 */

const EnemyConfig = {
  
  /**
   * 士兵 - 基础地面单位
   */
  soldier: {
    id: "soldier",
    name: "士兵",
    description: "基础的地面作战单位",
    type: "ground",
    
    // 基础属性
    baseStats: {
      hp: 100,
      speed: 50,        // 像素/秒
      armor: 0,
      magicResist: 0,
      reward: 12,       // 击杀奖励金币
      exp: 5,           // 击杀奖励经验
      damage: 1         // 到达终点扣除生命值
    },
    
    // 视觉配置
    visual: {
      size: 24,
      color: "#8B4513",
      sprite: "soldier.png",
      animationFrames: 4,
      animationSpeed: 200
    },
    
    // 移动配置
    movement: {
      canFly: false,
      canSwim: false,
      pathfinding: "simple"
    },
    
    // 抗性
    resistances: {
      physical: 0,      // 物理抗性 (0-1)
      magic: 0,         // 魔法抗性
      ice: 0,           // 冰冻抗性
      poison: 0,        // 毒素抗性
      slow: 0           // 减速抗性
    },
    
    // 特殊能力
    abilities: []
  },

  /**
   * 弓箭手 - 快速地面单位
   */
  archer: {
    id: "archer",
    name: "弓箭手",
    description: "移动速度快的远程单位",
    type: "ground",
    
    baseStats: {
      hp: 70,
      speed: 70,
      armor: 0,
      magicResist: 0,
      reward: 18,
      exp: 8,
      damage: 1
    },
    
    visual: {
      size: 22,
      color: "#228B22",
      sprite: "archer.png",
      animationFrames: 4,
      animationSpeed: 150
    },
    
    movement: {
      canFly: false,
      canSwim: false,
      pathfinding: "simple"
    },
    
    resistances: {
      physical: 0,
      magic: 0,
      ice: 0.1,         // 轻微冰冻抗性
      poison: 0,
      slow: 0.2         // 轻微减速抗性
    },
    
    abilities: []
  },

  /**
   * 重甲兵 - 高血量高护甲
   */
  heavy_soldier: {
    id: "heavy_soldier",
    name: "重甲兵",
    description: "血量和护甲都很高的重型单位",
    type: "ground",
    
    baseStats: {
      hp: 300,
      speed: 30,
      armor: 15,
      magicResist: 5,
      reward: 35,
      exp: 15,
      damage: 2
    },
    
    visual: {
      size: 28,
      color: "#2F4F4F",
      sprite: "heavy_soldier.png",
      animationFrames: 4,
      animationSpeed: 300
    },
    
    movement: {
      canFly: false,
      canSwim: false,
      pathfinding: "simple"
    },
    
    resistances: {
      physical: 0.3,    // 高物理抗性
      magic: 0.1,
      ice: 0.4,         // 高冰冻抗性
      poison: 0.2,
      slow: 0.5         // 高减速抗性
    },
    
    abilities: ["heavy_armor"]
  },

  /**
   * 飞行兵 - 空中单位
   */
  flying_unit: {
    id: "flying_unit",
    name: "飞行兵",
    description: "空中单位，只能被特定塔攻击",
    type: "air",
    
    baseStats: {
      hp: 120,
      speed: 80,
      armor: 0,
      magicResist: 10,
      reward: 25,
      exp: 12,
      damage: 1
    },
    
    visual: {
      size: 26,
      color: "#4169E1",
      sprite: "flying_unit.png",
      animationFrames: 6,
      animationSpeed: 100
    },
    
    movement: {
      canFly: true,
      canSwim: true,
      pathfinding: "flying"
    },
    
    resistances: {
      physical: 0.2,
      magic: 0,
      ice: 0,
      poison: 0.8,      // 高毒素抗性
      slow: 0.3
    },
    
    abilities: ["flying"]
  },

  /**
   * 快速兵 - 极高速度
   */
  speedster: {
    id: "speedster",
    name: "快速兵",
    description: "移动速度极快的轻型单位",
    type: "ground",
    
    baseStats: {
      hp: 50,
      speed: 120,
      armor: 0,
      magicResist: 0,
      reward: 20,
      exp: 10,
      damage: 1
    },
    
    visual: {
      size: 20,
      color: "#FF6347",
      sprite: "speedster.png",
      animationFrames: 6,
      animationSpeed: 80
    },
    
    movement: {
      canFly: false,
      canSwim: false,
      pathfinding: "simple"
    },
    
    resistances: {
      physical: 0,
      magic: 0,
      ice: 0,
      poison: 0,
      slow: 0.6         // 高减速抗性
    },
    
    abilities: ["speed_boost"]
  },

  /**
   * BOSS - 关卡首领
   */
  boss_tank: {
    id: "boss_tank",
    name: "装甲首领",
    description: "拥有大量血量和特殊技能的BOSS",
    type: "ground",
    
    baseStats: {
      hp: 1500,
      speed: 25,
      armor: 25,
      magicResist: 15,
      reward: 200,
      exp: 100,
      damage: 5
    },
    
    visual: {
      size: 48,
      color: "#8B0000",
      sprite: "boss_tank.png",
      animationFrames: 8,
      animationSpeed: 200
    },
    
    movement: {
      canFly: false,
      canSwim: false,
      pathfinding: "simple"
    },
    
    resistances: {
      physical: 0.4,
      magic: 0.3,
      ice: 0.7,
      poison: 0.5,
      slow: 0.8
    },
    
    abilities: ["boss_aura", "regeneration", "armor_boost"]
  }
}

/**
 * 敌人能力定义
 */
const EnemyAbilities = {
  heavy_armor: {
    name: "重甲",
    description: "减少受到的物理伤害",
    effect: "physical_resistance"
  },
  
  flying: {
    name: "飞行",
    description: "可以飞越障碍物，只能被特定塔攻击",
    effect: "air_unit"
  },
  
  speed_boost: {
    name: "冲刺",
    description: "在受到攻击时短暂提升移动速度",
    effect: "speed_on_hit"
  },
  
  boss_aura: {
    name: "首领光环",
    description: "为周围敌人提供护甲加成",
    effect: "armor_aura"
  },
  
  regeneration: {
    name: "再生",
    description: "持续恢复生命值",
    effect: "heal_over_time"
  },
  
  armor_boost: {
    name: "护甲强化",
    description: "血量降低时提升护甲",
    effect: "armor_on_low_hp"
  }
}

/**
 * 获取敌人配置
 */
function getEnemyConfig(enemyId) {
  return EnemyConfig[enemyId] || null
}

/**
 * 获取所有敌人类型
 */
function getAllEnemyTypes() {
  return Object.keys(EnemyConfig)
}

/**
 * 根据难度调整敌人属性
 */
function getScaledEnemyStats(enemyId, difficultyMultiplier = 1) {
  const config = getEnemyConfig(enemyId)
  if (!config) return null
  
  const scaledStats = { ...config.baseStats }
  scaledStats.hp = Math.floor(scaledStats.hp * difficultyMultiplier)
  scaledStats.armor = Math.floor(scaledStats.armor * difficultyMultiplier)
  scaledStats.reward = Math.floor(scaledStats.reward * difficultyMultiplier)
  
  return {
    ...config,
    baseStats: scaledStats
  }
}

/**
 * 获取敌人能力信息
 */
function getEnemyAbility(abilityId) {
  return EnemyAbilities[abilityId] || null
}

/**
 * 检查敌人是否有特定能力
 */
function hasAbility(enemyId, abilityId) {
  const config = getEnemyConfig(enemyId)
  return config && config.abilities.includes(abilityId)
}

/**
 * 获取敌人类型分类
 */
function getEnemiesByType(type) {
  const enemies = []
  for (let enemyId in EnemyConfig) {
    if (EnemyConfig[enemyId].type === type) {
      enemies.push(enemyId)
    }
  }
  return enemies
}

// 导出模块
module.exports = {
  EnemyConfig,
  EnemyAbilities,
  getEnemyConfig,
  getAllEnemyTypes,
  getScaledEnemyStats,
  getEnemyAbility,
  hasAbility,
  getEnemiesByType
}
