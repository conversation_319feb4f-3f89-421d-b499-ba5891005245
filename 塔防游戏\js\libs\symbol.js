/**
 * Symbol polyfill for WeChat Mini Game
 */

// 检查全局环境
const globalObj = (function() {
  if (typeof GameGlobal !== 'undefined') {
    return GameGlobal
  } else if (typeof global !== 'undefined') {
    return global
  } else if (typeof window !== 'undefined') {
    return window
  } else {
    return this
  }
})()

if (!globalObj.Symbol) {
  globalObj.Symbol = {}

  let symbolCounter = 0

  globalObj.Symbol = function Symbol(description) {
    return `__symbol_${symbolCounter++}_${description || ''}`
  }

  globalObj.Symbol.for = function(key) {
    return `__symbol_for_${key}`
  }

  globalObj.Symbol.keyFor = function(symbol) {
    if (typeof symbol === 'string' && symbol.startsWith('__symbol_for_')) {
      return symbol.replace('__symbol_for_', '')
    }
    return undefined
  }

  // 常用的内置Symbol
  globalObj.Symbol.iterator = '__symbol_iterator'
  globalObj.Symbol.toStringTag = '__symbol_toStringTag'
  globalObj.Symbol.hasInstance = '__symbol_hasInstance'
  globalObj.Symbol.species = '__symbol_species'
  globalObj.Symbol.toPrimitive = '__symbol_toPrimitive'
}
