/**
 * 子弹基类
 * 所有子弹和投射物的基类
 */
const BaseEntity = require('./BaseEntity.js')

class Bullet extends BaseEntity {
  /**
   * 构造函数
   * @param {number} x - 起始x坐标
   * @param {number} y - 起始y坐标
   * @param {Object} target - 目标敌人
   * @param {Object} config - 子弹配置
   */
  constructor(x, y, target, config) {
    super(x, y)
    
    // 基本属性
    this.type = 'bullet'
    this.target = target
    this.damage = config.damage || 10
    this.speed = config.speed || 200
    this.bulletType = config.bulletType || 'arrow'
    this.damageType = config.damageType || 'physical'
    
    // 特殊效果
    this.effects = config.effects || []
    this.splashRadius = config.splashRadius || 0
    this.splashDamage = config.splashDamage || 0
    
    // 移动相关
    this.velocity = { x: 0, y: 0 }
    this.targetPosition = null  // 目标位置（用于预测）
    this.isHoming = config.isHoming || false  // 是否追踪目标
    
    // 视觉相关
    this.color = config.color || '#ffff00'
    this.size = config.size || 4
    this.trail = []  // 轨迹
    this.maxTrailLength = 5
    
    // 生命周期
    this.maxLifetime = config.maxLifetime || 3000  // 最大存在时间（毫秒）
    this.hasHit = false
    
    // 设置初始方向和速度
    this.calculateInitialVelocity()
    
    // 更新大小
    this.setSize(this.size, this.size)
  }
  
  /**
   * 计算初始速度
   */
  calculateInitialVelocity() {
    if (!this.target) {
      // 没有目标，直接向前飞行
      this.velocity.x = this.speed
      this.velocity.y = 0
      return
    }
    
    // 预测目标位置（简单预测）
    let targetX = this.target.x
    let targetY = this.target.y
    
    if (this.target.velocity) {
      const timeToTarget = this.distanceTo(this.target) / this.speed
      targetX += this.target.velocity.x * timeToTarget
      targetY += this.target.velocity.y * timeToTarget
    }
    
    this.targetPosition = { x: targetX, y: targetY }
    
    // 计算方向
    const dx = targetX - this.x
    const dy = targetY - this.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    if (distance > 0) {
      this.velocity.x = (dx / distance) * this.speed
      this.velocity.y = (dy / distance) * this.speed
      
      // 设置朝向
      this.rotation = Math.atan2(dy, dx)
    }
  }
  
  /**
   * 更新子弹状态
   */
  update(deltaTime) {
    super.update(deltaTime)
    
    // 检查生命周期
    if (this.age > this.maxLifetime) {
      this.destroy()
      return
    }
    
    // 如果已经命中，不再移动
    if (this.hasHit) {
      return
    }
    
    // 更新轨迹
    this.updateTrail()
    
    // 如果是追踪子弹，更新方向
    if (this.isHoming && this.target && this.target.isAlive) {
      this.updateHoming(deltaTime)
    }
    
    // 移动
    this.move(deltaTime)
    
    // 检查碰撞
    this.checkCollision()
  }
  
  /**
   * 更新追踪逻辑
   */
  updateHoming(deltaTime) {
    if (!this.target || !this.target.isAlive) {
      return
    }
    
    // 计算到目标的方向
    const dx = this.target.x - this.x
    const dy = this.target.y - this.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    if (distance > 0) {
      // 计算目标方向
      const targetVelX = (dx / distance) * this.speed
      const targetVelY = (dy / distance) * this.speed
      
      // 平滑转向（避免急转弯）
      const turnRate = 0.1  // 转向速度
      this.velocity.x += (targetVelX - this.velocity.x) * turnRate
      this.velocity.y += (targetVelY - this.velocity.y) * turnRate
      
      // 重新归一化速度
      const currentSpeed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y)
      if (currentSpeed > 0) {
        this.velocity.x = (this.velocity.x / currentSpeed) * this.speed
        this.velocity.y = (this.velocity.y / currentSpeed) * this.speed
      }
      
      // 更新朝向
      this.rotation = Math.atan2(this.velocity.y, this.velocity.x)
    }
  }
  
  /**
   * 移动子弹
   */
  move(deltaTime) {
    const moveX = this.velocity.x * deltaTime / 1000
    const moveY = this.velocity.y * deltaTime / 1000
    
    this.x += moveX
    this.y += moveY
  }
  
  /**
   * 更新轨迹
   */
  updateTrail() {
    // 添加当前位置到轨迹
    this.trail.push({ x: this.x, y: this.y })
    
    // 限制轨迹长度
    if (this.trail.length > this.maxTrailLength) {
      this.trail.shift()
    }
  }
  
  /**
   * 检查碰撞
   */
  checkCollision() {
    if (!this.target || !this.target.isAlive) {
      return
    }
    
    // 检查是否命中目标
    const distance = this.distanceTo(this.target)
    if (distance <= this.target.radius + this.radius) {
      this.hit(this.target)
    }
  }
  
  /**
   * 命中目标
   * @param {Object} target - 被命中的目标
   */
  hit(target) {
    if (this.hasHit) return
    
    this.hasHit = true
    
    // 触发命中事件
    if (this.onHit) {
      this.onHit(this, target)
    }
    
    // 创建命中特效
    this.createHitEffect()
    
    // 销毁子弹
    this.destroy()
  }
  
  /**
   * 创建命中特效
   */
  createHitEffect() {
    // 这里可以创建爆炸、闪光等特效
    // 暂时简化处理
    if (this.onCreateEffect) {
      this.onCreateEffect({
        type: 'hit',
        x: this.x,
        y: this.y,
        bulletType: this.bulletType
      })
    }
  }
  
  /**
   * 绘制子弹
   */
  draw(ctx) {
    // 绘制轨迹
    if (this.trail.length > 1) {
      this.drawTrail(ctx)
    }
    
    // 根据子弹类型绘制
    switch (this.bulletType) {
      case 'arrow':
        this.drawArrow(ctx)
        break
      case 'cannonball':
        this.drawCannonball(ctx)
        break
      case 'magic_bolt':
        this.drawMagicBolt(ctx)
        break
      case 'ice_shard':
        this.drawIceShard(ctx)
        break
      case 'poison_dart':
        this.drawPoisonDart(ctx)
        break
      default:
        this.drawDefault(ctx)
    }
  }
  
  /**
   * 绘制轨迹
   */
  drawTrail(ctx) {
    if (this.trail.length < 2) return
    
    ctx.strokeStyle = this.color
    ctx.lineWidth = 2
    ctx.globalAlpha = 0.5
    
    ctx.beginPath()
    ctx.moveTo(this.trail[0].x - this.x, this.trail[0].y - this.y)
    
    for (let i = 1; i < this.trail.length; i++) {
      ctx.lineTo(this.trail[i].x - this.x, this.trail[i].y - this.y)
    }
    
    ctx.stroke()
    ctx.globalAlpha = 1
  }
  
  /**
   * 绘制箭矢
   */
  drawArrow(ctx) {
    ctx.fillStyle = '#8B4513'
    ctx.strokeStyle = '#654321'
    ctx.lineWidth = 1
    
    // 箭身
    ctx.fillRect(-8, -1, 12, 2)
    
    // 箭头
    ctx.beginPath()
    ctx.moveTo(4, 0)
    ctx.lineTo(-2, -3)
    ctx.lineTo(-2, 3)
    ctx.closePath()
    ctx.fill()
    ctx.stroke()
  }
  
  /**
   * 绘制炮弹
   */
  drawCannonball(ctx) {
    ctx.fillStyle = '#2F4F4F'
    ctx.strokeStyle = '#000000'
    ctx.lineWidth = 1
    
    ctx.beginPath()
    ctx.arc(0, 0, this.size, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()
  }
  
  /**
   * 绘制魔法球
   */
  drawMagicBolt(ctx) {
    // 外层光环
    ctx.fillStyle = 'rgba(147, 112, 219, 0.3)'
    ctx.beginPath()
    ctx.arc(0, 0, this.size + 2, 0, Math.PI * 2)
    ctx.fill()
    
    // 内核
    ctx.fillStyle = '#9370DB'
    ctx.beginPath()
    ctx.arc(0, 0, this.size, 0, Math.PI * 2)
    ctx.fill()
    
    // 闪光效果
    ctx.fillStyle = '#ffffff'
    ctx.beginPath()
    ctx.arc(-1, -1, this.size / 3, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制冰锥
   */
  drawIceShard(ctx) {
    ctx.fillStyle = '#87CEEB'
    ctx.strokeStyle = '#4682B4'
    ctx.lineWidth = 1
    
    // 冰锥形状
    ctx.beginPath()
    ctx.moveTo(6, 0)
    ctx.lineTo(-3, -2)
    ctx.lineTo(-6, 0)
    ctx.lineTo(-3, 2)
    ctx.closePath()
    ctx.fill()
    ctx.stroke()
  }
  
  /**
   * 绘制毒镖
   */
  drawPoisonDart(ctx) {
    ctx.fillStyle = '#9ACD32'
    ctx.strokeStyle = '#556B2F'
    ctx.lineWidth = 1
    
    // 镖身
    ctx.fillRect(-6, -1, 8, 2)
    
    // 镖头
    ctx.beginPath()
    ctx.moveTo(2, 0)
    ctx.lineTo(-2, -2)
    ctx.lineTo(-2, 2)
    ctx.closePath()
    ctx.fill()
    ctx.stroke()
    
    // 毒液效果
    ctx.fillStyle = 'rgba(154, 205, 50, 0.5)'
    ctx.beginPath()
    ctx.arc(0, 0, this.size + 1, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制默认子弹
   */
  drawDefault(ctx) {
    ctx.fillStyle = this.color
    ctx.beginPath()
    ctx.arc(0, 0, this.size, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 获取子弹信息
   */
  getBulletInfo() {
    return {
      id: this.id,
      type: this.type,
      bulletType: this.bulletType,
      damage: this.damage,
      damageType: this.damageType,
      position: { x: this.x, y: this.y },
      velocity: { ...this.velocity },
      target: this.target ? this.target.id : null,
      effects: this.effects,
      splashRadius: this.splashRadius,
      age: this.age,
      hasHit: this.hasHit
    }
  }
  
  /**
   * 重置子弹（用于对象池）
   */
  reset(x, y, target, config) {
    // 重置基础属性
    this.x = x
    this.y = y
    this.target = target
    this.damage = config.damage || 10
    this.speed = config.speed || 200
    this.bulletType = config.bulletType || 'arrow'
    this.damageType = config.damageType || 'physical'
    
    // 重置状态
    this.isAlive = true
    this.isVisible = true
    this.hasHit = false
    this.age = 0
    
    // 重置特效
    this.effects = config.effects || []
    this.splashRadius = config.splashRadius || 0
    this.splashDamage = config.splashDamage || 0
    
    // 重置轨迹
    this.trail = []
    
    // 重新计算速度
    this.calculateInitialVelocity()
  }
}

// 导出模块
module.exports = Bullet
