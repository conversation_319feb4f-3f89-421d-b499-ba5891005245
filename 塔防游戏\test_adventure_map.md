# 冒险关卡地图测试说明

## 功能概述

新增的冒险关卡地图功能包括：
- 20个关卡的横向滚动地图
- 左右拖动交互
- 惯性滚动效果
- 关卡解锁状态显示
- 星级评价系统
- 进度指示器

## 测试步骤

### 1. 进入冒险关卡地图
1. 启动游戏，进入登录页面
2. 点击"开始游戏"进入主页面
3. 点击底部导航的"挑战"按钮
4. 在挑战页面点击"冒险关卡"卡片
5. 应该能正确进入冒险关卡地图页面

### 2. 测试拖动功能
1. 在关卡地图页面，用手指左右拖动
2. 验证关卡卡片能跟随手指移动
3. 快速滑动后松手，验证惯性滚动效果
4. 拖动到边界时，验证弹性回弹效果

### 3. 测试关卡显示
1. 验证前5个关卡显示为已解锁状态
2. 验证后15个关卡显示为锁定状态（有锁图标）
3. 验证已解锁关卡显示星级评价
4. 验证关卡编号正确显示（1-20）
5. 验证难度颜色：
   - 关卡1-5：绿色（简单）
   - 关卡6-10：蓝色（普通）
   - 关卡11-15：橙色（困难）
   - 关卡16-20：红色（专家）

### 4. 测试交互功能
1. 点击已解锁的关卡，应该弹出关卡详情对话框
2. 点击未解锁的关卡，应该提示"关卡未解锁"
3. 点击左上角返回按钮，应该返回挑战页面

### 5. 测试进度指示器
1. 验证底部进度条正确显示滚动进度
2. 验证进度文字显示"5/20"（已解锁/总关卡）

## 预期效果

### 视觉效果
- 蓝色渐变背景
- 粒子动画效果
- 关卡卡片有阴影和圆角
- 已解锁关卡有彩色背景
- 未解锁关卡为灰色

### 交互效果
- 拖动响应灵敏
- 惯性滚动自然流畅
- 边界回弹有弹性感
- 按钮点击有视觉反馈

### 性能要求
- 拖动过程无卡顿
- 帧率保持在50FPS以上
- 内存使用稳定

## 常见问题排查

### 1. 无法进入冒险关卡地图
- 检查ChallengeScene.js中的onDungeonClick方法
- 确认main.js中已注册AdventureMapScene

### 2. 拖动不响应
- 检查触摸事件绑定
- 确认拖动逻辑正确实现

### 3. 关卡显示异常
- 检查关卡数据初始化
- 确认渲染逻辑正确

### 4. 性能问题
- 检查是否有不必要的重复渲染
- 优化粒子数量
- 确认事件监听器正确清理

## 成功标准

- [ ] 所有拖动交互正常工作
- [ ] 关卡状态正确显示
- [ ] 性能流畅无卡顿
- [ ] 所有按钮功能正常
- [ ] 场景切换正常
- [ ] 无JavaScript错误

## 下一步开发建议

1. 添加关卡预览图片
2. 实现关卡选择音效
3. 添加关卡完成动画
4. 优化拖动手感
5. 添加关卡描述信息
