/**
 * 微信小游戏屏幕适配器
 * 基于官方文档的标准适配方案
 */
class ScreenAdapter {
  constructor() {
    // 设计基准尺寸 (横屏)
    this.DESIGN_WIDTH = 1334
    this.DESIGN_HEIGHT = 750

    // 获取系统信息
    this.systemInfo = wx.getSystemInfoSync()

    // 屏幕基本信息
    this.screenWidth = this.systemInfo.screenWidth
    this.screenHeight = this.systemInfo.screenHeight
    this.windowWidth = this.systemInfo.windowWidth
    this.windowHeight = this.systemInfo.windowHeight
    this.pixelRatio = this.systemInfo.pixelRatio

    // 计算适配比例
    this.calculateScale()
    
    // 安全区域信息
    this.safeArea = this.systemInfo.safeArea || {
      left: 0,
      top: 0,
      right: this.windowWidth,
      bottom: this.windowHeight,
      width: this.windowWidth,
      height: this.windowHeight
    }
    
    // 状态栏高度
    this.statusBarHeight = this.systemInfo.statusBarHeight || 0
    
    // 设备方向
    this.deviceOrientation = this.systemInfo.deviceOrientation || 'portrait'
  }

  /**
   * 计算适配比例
   */
  calculateScale() {
    // 计算缩放比例
    const scaleX = this.windowWidth / this.DESIGN_WIDTH
    const scaleY = this.windowHeight / this.DESIGN_HEIGHT

    // 使用较小的比例以确保内容完全显示
    this.scale = Math.min(scaleX, scaleY)

    // 计算实际显示尺寸
    this.actualWidth = this.DESIGN_WIDTH * this.scale
    this.actualHeight = this.DESIGN_HEIGHT * this.scale

    // 计算居中偏移
    this.offsetX = (this.windowWidth - this.actualWidth) / 2
    this.offsetY = (this.windowHeight - this.actualHeight) / 2

    console.log('屏幕适配信息:', {
      设计尺寸: `${this.DESIGN_WIDTH}x${this.DESIGN_HEIGHT}`,
      窗口尺寸: `${this.windowWidth}x${this.windowHeight}`,
      缩放比例: this.scale,
      实际尺寸: `${this.actualWidth}x${this.actualHeight}`,
      偏移: `${this.offsetX}, ${this.offsetY}`
    })
  }
  
  /**
   * 获取设计宽度
   */
  getDesignWidth() {
    return this.DESIGN_WIDTH
  }

  /**
   * 获取设计高度
   */
  getDesignHeight() {
    return this.DESIGN_HEIGHT
  }

  /**
   * 获取窗口宽度
   */
  getWindowWidth() {
    return this.windowWidth
  }

  /**
   * 获取窗口高度
   */
  getWindowHeight() {
    return this.windowHeight
  }

  /**
   * 获取缩放比例
   */
  getScale() {
    return this.scale
  }

  /**
   * 获取像素比
   */
  getPixelRatio() {
    return this.pixelRatio
  }

  /**
   * 将设计坐标转换为屏幕坐标
   */
  designToScreen(x, y) {
    return {
      x: x * this.scale + this.offsetX,
      y: y * this.scale + this.offsetY
    }
  }

  /**
   * 将屏幕坐标转换为设计坐标
   */
  screenToDesign(x, y) {
    return {
      x: (x - this.offsetX) / this.scale,
      y: (y - this.offsetY) / this.scale
    }
  }
  
  /**
   * 获取安全区域顶部位置
   */
  getSafeAreaTop() {
    return this.safeArea.top
  }
  
  /**
   * 获取安全区域底部位置
   */
  getSafeAreaBottom() {
    return this.safeArea.bottom
  }
  
  /**
   * 获取安全区域左侧位置
   */
  getSafeAreaLeft() {
    return this.safeArea.left
  }
  
  /**
   * 获取安全区域右侧位置
   */
  getSafeAreaRight() {
    return this.safeArea.right
  }
  
  /**
   * 获取安全区域宽度
   */
  getSafeAreaWidth() {
    return this.safeArea.width
  }
  
  /**
   * 获取安全区域高度
   */
  getSafeAreaHeight() {
    return this.safeArea.height
  }
  
  /**
   * 获取状态栏高度
   */
  getStatusBarHeight() {
    return this.statusBarHeight
  }
  
  /**
   * 获取设备像素比
   */
  getPixelRatio() {
    return this.pixelRatio
  }
  
  /**
   * 是否为横屏
   */
  isLandscape() {
    return this.deviceOrientation === 'landscape'
  }
  
  /**
   * 是否为竖屏
   */
  isPortrait() {
    return this.deviceOrientation === 'portrait'
  }
  
  /**
   * 获取Canvas的实际尺寸（考虑像素比）
   */
  getCanvasSize() {
    return {
      width: this.windowWidth * this.pixelRatio,
      height: this.windowHeight * this.pixelRatio
    }
  }
  
  /**
   * 设置Canvas尺寸和样式
   */
  setupCanvas(canvas) {
    // 设置Canvas的实际尺寸（使用设计尺寸 * 像素比）
    canvas.width = this.DESIGN_WIDTH * this.pixelRatio
    canvas.height = this.DESIGN_HEIGHT * this.pixelRatio

    // 设置Canvas的显示尺寸
    canvas.style = canvas.style || {}
    canvas.style.width = this.windowWidth + 'px'
    canvas.style.height = this.windowHeight + 'px'

    // 获取2D上下文并设置缩放
    const ctx = canvas.getContext('2d')
    ctx.scale(this.pixelRatio, this.pixelRatio)

    console.log('Canvas设置完成:', {
      Canvas尺寸: `${canvas.width}x${canvas.height}`,
      显示尺寸: `${this.windowWidth}x${this.windowHeight}`,
      设计尺寸: `${this.DESIGN_WIDTH}x${this.DESIGN_HEIGHT}`,
      像素比: this.pixelRatio
    })

    return canvas
  }
  
  /**
   * 转换触摸坐标到设计坐标
   */
  convertTouchToDesign(touch) {
    // 先转换为屏幕坐标，再转换为设计坐标
    return this.screenToDesign(touch.pageX, touch.pageY)
  }

  /**
   * 转换触摸坐标到Canvas坐标（保持兼容性）
   */
  convertTouchToCanvas(touch) {
    const designPos = this.convertTouchToDesign(touch)
    return {
      x: designPos.x,
      y: designPos.y
    }
  }
  
  /**
   * 转换Canvas坐标到显示坐标
   */
  convertCanvasToDisplay(x, y) {
    return {
      x: x / this.pixelRatio,
      y: y / this.pixelRatio
    }
  }
}

// 导出模块
module.exports = ScreenAdapter
