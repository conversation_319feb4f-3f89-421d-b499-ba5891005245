/**
 * 基础实体类
 * 所有游戏对象的基类
 */
class BaseEntity {
  constructor(x = 0, y = 0) {
    // 基础属性
    this.id = this.generateId()
    this.x = x
    this.y = y
    this.width = 32
    this.height = 32
    this.radius = 16  // 碰撞半径
    
    // 状态
    this.isAlive = true
    this.isVisible = true
    this.type = 'entity'
    
    // 渲染相关
    this.rotation = 0
    this.scale = 1
    this.alpha = 1
    
    // 时间相关
    this.age = 0  // 存在时间
    this.lastUpdateTime = 0
  }
  
  /**
   * 生成唯一ID
   */
  generateId() {
    return 'entity_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  /**
   * 更新实体
   */
  update(deltaTime) {
    this.age += deltaTime
    this.lastUpdateTime = Date.now()
  }
  
  /**
   * 渲染实体
   */
  render(ctx) {
    if (!this.isVisible || !this.isAlive) return
    
    ctx.save()
    ctx.globalAlpha = this.alpha
    ctx.translate(this.x, this.y)
    ctx.rotate(this.rotation)
    ctx.scale(this.scale, this.scale)
    
    // 子类重写此方法
    this.draw(ctx)
    
    ctx.restore()
  }
  
  /**
   * 绘制方法，子类重写
   */
  draw(ctx) {
    // 默认绘制一个矩形
    ctx.fillStyle = '#ff0000'
    ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height)
  }
  
  /**
   * 获取中心点
   */
  getCenter() {
    return {
      x: this.x,
      y: this.y
    }
  }
  
  /**
   * 获取边界框
   */
  getBounds() {
    return {
      left: this.x - this.width / 2,
      right: this.x + this.width / 2,
      top: this.y - this.height / 2,
      bottom: this.y + this.height / 2
    }
  }
  
  /**
   * 检查点是否在实体内
   */
  containsPoint(x, y) {
    const bounds = this.getBounds()
    return x >= bounds.left && x <= bounds.right && 
           y >= bounds.top && y <= bounds.bottom
  }
  
  /**
   * 计算到另一个实体的距离
   */
  distanceTo(other) {
    const dx = this.x - other.x
    const dy = this.y - other.y
    return Math.sqrt(dx * dx + dy * dy)
  }
  
  /**
   * 计算到点的距离
   */
  distanceToPoint(x, y) {
    const dx = this.x - x
    const dy = this.y - y
    return Math.sqrt(dx * dx + dy * dy)
  }
  
  /**
   * 移动到指定位置
   */
  moveTo(x, y) {
    this.x = x
    this.y = y
  }
  
  /**
   * 相对移动
   */
  moveBy(dx, dy) {
    this.x += dx
    this.y += dy
  }
  
  /**
   * 朝向另一个实体
   */
  lookAt(target) {
    const dx = target.x - this.x
    const dy = target.y - this.y
    this.rotation = Math.atan2(dy, dx)
  }
  
  /**
   * 朝向指定点
   */
  lookAtPoint(x, y) {
    const dx = x - this.x
    const dy = y - this.y
    this.rotation = Math.atan2(dy, dx)
  }
  
  /**
   * 检查是否与另一个实体碰撞
   */
  collidesWith(other) {
    const distance = this.distanceTo(other)
    return distance < (this.radius + other.radius)
  }
  
  /**
   * 设置位置
   */
  setPosition(x, y) {
    this.x = x
    this.y = y
  }
  
  /**
   * 设置大小
   */
  setSize(width, height) {
    this.width = width
    this.height = height
    this.radius = Math.min(width, height) / 2
  }
  
  /**
   * 销毁实体
   */
  destroy() {
    this.isAlive = false
    this.isVisible = false
    
    // 清理资源
    this.cleanup()
  }
  
  /**
   * 清理资源，子类重写
   */
  cleanup() {
    // 子类实现具体的清理逻辑
  }
  
  /**
   * 获取实体信息
   */
  getInfo() {
    return {
      id: this.id,
      type: this.type,
      position: { x: this.x, y: this.y },
      size: { width: this.width, height: this.height },
      isAlive: this.isAlive,
      age: this.age
    }
  }
  
  /**
   * 序列化实体数据
   */
  serialize() {
    return {
      id: this.id,
      type: this.type,
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height,
      rotation: this.rotation,
      scale: this.scale,
      alpha: this.alpha,
      isAlive: this.isAlive,
      isVisible: this.isVisible
    }
  }
  
  /**
   * 从序列化数据恢复
   */
  deserialize(data) {
    this.id = data.id
    this.type = data.type
    this.x = data.x
    this.y = data.y
    this.width = data.width
    this.height = data.height
    this.rotation = data.rotation
    this.scale = data.scale
    this.alpha = data.alpha
    this.isAlive = data.isAlive
    this.isVisible = data.isVisible
  }
}

// 导出模块
module.exports = BaseEntity
