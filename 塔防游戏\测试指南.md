# 塔防游戏测试指南

## 快速开始

### 1. 环境准备
- 下载并安装微信开发者工具
- 确保工具版本支持小游戏开发

### 2. 导入项目
1. 打开微信开发者工具
2. 选择"小游戏"
3. 点击"导入项目"
4. 选择 `塔防游戏` 文件夹
5. 填写项目信息（AppID可以使用测试号）

### 3. 编译运行
1. 点击"编译"按钮
2. 在模拟器中查看效果
3. 可以切换不同设备型号测试适配

## 功能测试

### 登录页面测试
- [x] 页面正常显示
- [x] 背景渐变效果
- [x] 粒子动画效果
- [x] 按钮交互响应
- [x] 触摸事件处理
- [x] 屏幕适配效果

### 屏幕适配测试
- [x] iPhone 6/7/8 (375x667)
- [x] iPhone 6/7/8 Plus (414x736)
- [x] iPhone X/11/12 (375x812)
- [x] Android 各种尺寸
- [x] 横屏显示效果
- [x] 刘海屏适配

### 场景切换测试
- [x] 登录页面 → 游戏页面
- [x] 游戏页面 → 登录页面
- [x] 场景切换动画
- [x] 内存清理

### 游戏页面测试
- [x] 基础UI显示
- [x] 暂停/继续功能
- [x] 返回按钮功能
- [x] 分数和时间显示

### 冒险关卡地图测试（新功能）
- [ ] 从挑战页面正确进入冒险关卡地图
- [ ] 关卡卡片正确显示（20个关卡）
- [ ] 左右拖动功能正常
- [ ] 拖动惯性滚动效果流畅
- [ ] 边界回弹效果正常
- [ ] 关卡解锁状态正确显示
- [ ] 星级评价正确显示
- [ ] 难度标识正确显示
- [ ] 进度指示器正常工作
- [ ] 点击已解锁关卡弹出详情
- [ ] 点击未解锁关卡提示正确
- [ ] 返回按钮功能正常

## 性能测试

### 帧率测试
- 目标: 60FPS
- 测试方法: 开发者工具性能面板
- 关注点: CPU使用率、内存占用

### 内存测试
- 场景切换内存释放
- 长时间运行内存稳定性
- 粒子效果内存占用

### 兼容性测试
- 不同iOS版本
- 不同Android版本
- 不同屏幕分辨率
- 不同像素密度

### 冒险关卡地图性能测试
- [ ] 拖动过程流畅，无卡顿
- [ ] 大量关卡渲染性能良好
- [ ] 粒子动画不影响拖动性能
- [ ] 内存使用稳定
- [ ] 快速连续拖动的处理
- [ ] 边界情况处理正确

## 调试技巧

### 1. 控制台调试
```javascript
// 在代码中添加调试信息
console.log('屏幕适配信息:', this.adapter)
console.log('触摸位置:', pos)
```

### 2. 性能监控
- 使用微信开发者工具的性能面板
- 监控FPS、CPU、内存使用情况
- 分析渲染性能瓶颈

### 3. 真机调试
- 使用真机调试功能
- 测试不同设备的实际表现
- 验证触摸响应和适配效果

## 常见问题

### 1. 屏幕适配问题
**问题**: UI元素位置不正确
**解决**: 检查ScreenAdapter的scale计算

### 2. 触摸事件问题
**问题**: 按钮点击无响应
**解决**: 检查触摸坐标转换逻辑

### 3. 性能问题
**问题**: 帧率过低
**解决**: 优化渲染逻辑，减少不必要的绘制

### 4. 内存泄漏
**问题**: 内存持续增长
**解决**: 检查事件监听器清理，对象引用释放

## 发布前检查清单

- [ ] 所有功能正常工作
- [ ] 多设备适配测试通过
- [ ] 性能指标达标
- [ ] 内存使用稳定
- [ ] 用户体验流畅
- [ ] 代码注释完整
- [ ] 错误处理完善

## 后续开发建议

1. **游戏内容扩展**
   - 实现完整的塔防游戏逻辑
   - 添加多种塔和敌人类型
   - 设计关卡系统

2. **UI/UX优化**
   - 添加音效和音乐
   - 实现更丰富的视觉效果
   - 优化用户交互体验

3. **功能完善**
   - 添加存档系统
   - 实现成就系统
   - 添加社交功能

4. **性能优化**
   - 实现对象池
   - 优化渲染管线
   - 减少内存分配
