# 卡片边框图片

## 图片文件

请将以下三张边框图片放入此目录：

- `border_blue.png` - 优雅蓝色边框 (稀有品质，60%概率) - #4A90E2
- `border_purple.png` - 深邃紫色边框 (史诗品质，30%概率) - #8E44AD
- `border_red.png` - 华丽橙金色边框 (传说品质，10%概率) - #E67E22

## 图片要求

- **尺寸**: 188x335px (与卡片实际尺寸一致，9:16比例)
- **格式**: PNG (支持透明度)
- **设计**: 边框图片，中间内容区域必须完全透明
- **颜色**: 蓝色、紫色、红色三种主题色
- **边框宽度**: 建议30px边框宽度，内容区域会自动缩进

## 设计说明

### 边框设计要点
- **外边框**: 可以设计装饰性的外边框
- **内容区域**: 中心区域保持透明，用于显示卡片内容
- **边框宽度**: 代码中设置为30px，卡片内容会自动向内缩进30px
- **视觉层级**: 边框显示在背景之上，但文字元素会显示在边框之上
- **透明度要求**: 内容区域（25-153px宽 × 40-295px高）必须完全透明，不能有任何颜色或装饰

### 内容区域计算
- **实际内容区域**: 128x245px (调整后尺寸)
- **水平偏移**: 向左偏移5px，适应不规则边框设计
- **垂直偏移**: 顶部向下缩进25px，底部向上缩进10px
- **内容居中**: 卡片内容会在偏移后的区域内居中显示
- **边框保护**: 30px边框宽度为边框设计提供极其充足的空间
- **不规则支持**: 支持左右、上下完全不对称的边框设计

### 空间分配
- **左边框**: 25px (30-5)
- **右边框**: 35px (30+5)
- **上边框**: 55px (30+25) ← 超大顶部装饰空间
- **下边框**: 40px (30+10)
- **内容区域**: 128x245px

## 使用说明

- 蓝色边框用于稀有英雄
- 紫色边框用于史诗英雄
- 红色边框用于传说英雄
- 普通英雄使用代码绘制的默认边框
