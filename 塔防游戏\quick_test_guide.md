# 塔防游戏快速测试指南

## 🚀 立即测试

### 第一步：启动游戏
1. 在微信开发者工具中打开项目
2. 点击"编译"按钮
3. 确认游戏正常启动

### 第二步：进入测试战斗场景
1. 点击"开始游戏"
2. 点击"挑战"
3. 点击"冒险关卡"
4. 点击第一个关卡
5. 点击"开始挑战"

### 第三步：测试基础功能
1. **建造塔**：点击地图上的空白区域建造塔（消耗50金币）
2. **观察敌人**：等待红色敌人从左侧生成并沿路径移动
3. **观察攻击**：塔会自动攻击范围内的敌人，发射黄色子弹
4. **观察战斗**：敌人被击杀后会给予金币和分数奖励

## 🎯 预期效果

### 视觉效果
- ✅ 绿色草地背景
- ✅ 棕色路径
- ✅ 棕色圆形塔
- ✅ 红色圆形敌人
- ✅ 黄色圆形子弹
- ✅ 敌人血条显示

### 游戏逻辑
- ✅ 点击建造塔，消耗50金币
- ✅ 敌人每2秒生成一个，总共5个
- ✅ 塔自动攻击范围内敌人
- ✅ 敌人被击杀获得10金币和10分数
- ✅ 敌人到达终点扣除1生命值

### UI显示
- ✅ 左上角显示金币、生命值、分数
- ✅ 底部显示操作说明
- ✅ 左上角返回按钮

## 🏆 成功标准

### 基础功能正常
- [ ] 游戏正常启动和场景切换
- [ ] 能够建造塔并消耗金币
- [ ] 敌人正常生成和移动
- [ ] 塔能够攻击敌人
- [ ] 战斗逻辑正确运行

### 胜利条件
- [ ] 消灭所有5个敌人后显示"测试完成"
- [ ] 点击确定返回关卡选择

### 失败条件
- [ ] 生命值归零时显示"测试失败"

## 🐛 常见问题

### 如果游戏无法启动
1. 检查控制台是否有错误信息
2. 确认所有文件路径正确
3. 重新编译项目

### 如果无法进入战斗场景
1. 检查AdventureMapScene是否正确设置关卡ID
2. 确认main.js中的场景切换逻辑

### 如果战斗功能异常
1. 检查TestBattleScene的逻辑
2. 查看控制台错误信息
3. 确认触摸事件绑定

## 📈 下一步

### 如果测试成功
1. 可以尝试切换到完整战斗场景（修改main.js中的'battle'为'battle-full'）
2. 测试更复杂的功能
3. 进行性能测试

### 如果测试失败
1. 查看错误信息
2. 检查文件完整性
3. 逐步调试问题

## 🔧 调试技巧

### 查看控制台
- 打开微信开发者工具的控制台
- 查看是否有JavaScript错误
- 注意红色错误信息

### 添加调试信息
在TestBattleScene.js中添加console.log来调试：
```javascript
console.log('塔数量:', this.towers.length)
console.log('敌人数量:', this.enemies.length)
console.log('金币:', this.gold)
```

### 检查文件
确认以下文件存在且无语法错误：
- js/scenes/TestBattleScene.js
- js/components/BackButton.js
- js/utils/ScreenAdapter.js
- js/utils/ImageManager.js

## 🎮 测试完成后

如果基础测试通过，说明：
1. ✅ 游戏框架正常工作
2. ✅ 场景切换系统正常
3. ✅ 基础战斗逻辑正确
4. ✅ UI交互响应正常

然后可以进行完整战斗系统的测试！
