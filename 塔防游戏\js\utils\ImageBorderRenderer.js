const imageManager = require('./ImageManager.js')

/**
 * 简化的图片边框渲染器
 * 只处理三张边框图片：蓝、紫、红
 */
class ImageBorderRenderer {
  constructor() {
    this.borderImages = {}
    this.isLoaded = false
    this.animationTime = 0  // 动画时间计数器
    this.particles = {}     // 存储每个卡片的粒子状态
  }

  /**
   * 初始化并预加载边框图片
   * @returns {Promise<boolean>} 是否加载成功
   */
  async initialize() {
    try {
      this.borderImages = await imageManager.preloadAllBorderImages()
      this.isLoaded = true
      return true
    } catch (error) {
      console.error('边框图片预加载失败:', error)
      this.isLoaded = false
      return false
    }
  }

  /**
   * 检查是否准备就绪
   */
  isReady() {
    return this.isLoaded
  }

  /**
   * 更新动画时间
   */
  updateAnimation() {
    this.animationTime += 16 // 假设60FPS，每帧约16ms
  }

  /**
   * 绘制图片边框
   * @param {CanvasRenderingContext2D} ctx Canvas上下文
   * @param {number} x 卡片X坐标
   * @param {number} y 卡片Y坐标
   * @param {number} width 卡片宽度
   * @param {number} height 卡片高度
   * @param {string} rarity 稀有度
   * @param {boolean} isPressed 是否按压状态
   */
  drawImageBorder(ctx, x, y, width, height, rarity, isPressed = false) {
    // 根据稀有度选择边框颜色
    const colorMap = {
      rare: 'blue',
      epic: 'purple',
      legendary: 'red'
    }

    const color = colorMap[rarity]

    ctx.save()

    // 如果是按压状态，应用滤镜效果
    if (isPressed) {
      ctx.filter = 'brightness(0.8)'
    }

    // 绘制边框图片或回退边框
    if (color && this.isLoaded && this.borderImages[color]) {
      ctx.drawImage(this.borderImages[color], x, y, width, height)
    } else {
      // 如果没有对应图片，绘制代码边框
      this.drawFallbackBorder(ctx, x, y, width, height, rarity, isPressed)
    }

    // 最后绘制粒子效果
    this.drawParticleEffect(ctx, x, y, width, height, rarity)

    ctx.restore()
  }
  

  


  /**
   * 绘制粒子效果 - 移动且一闪一闪的动画
   */
  drawParticleEffect(ctx, x, y, width, height, rarity) {
    const particleConfig = this.getParticleConfig(rarity)
    if (!particleConfig) return

    // 为每个卡片创建粒子初始状态
    const cardKey = `${x}_${y}_${rarity}`
    if (!this.particles[cardKey]) {
      this.particles[cardKey] = this.generateParticles(x, y, width, height, particleConfig)
    }

    const particles = this.particles[cardKey]

    ctx.save()
    ctx.fillStyle = particleConfig.color

    // 绘制移动的粒子，带有闪烁动画
    particles.forEach((particle, index) => {
      // 计算当前位置（慢速移动）
      const moveSpeed = 0.0005 // 移动速度（很慢）
      const currentX = particle.baseX + Math.sin(this.animationTime * moveSpeed + particle.offsetX) * particle.moveRangeX
      const currentY = particle.baseY + Math.cos(this.animationTime * moveSpeed + particle.offsetY) * particle.moveRangeY

      // 确保粒子仍在边框区域内
      const isInBorder = (currentX < x + 30 || currentX > x + width - 30 || currentY < y + 30 || currentY > y + height - 30)
      if (!isInBorder) return

      // 闪烁效果
      const flickerSpeed = 2000 + (index * 300) // 2-5秒的闪烁周期
      const phase = (this.animationTime + index * 500) % flickerSpeed
      const alpha = (Math.sin(phase / flickerSpeed * Math.PI * 2) + 1) / 2 // 0-1之间的正弦波

      // 让透明度在0.1-0.8之间变化，实现一闪一闪的效果
      ctx.globalAlpha = 0.1 + alpha * 0.7

      ctx.beginPath()
      ctx.arc(currentX, currentY, particle.size, 0, Math.PI * 2)
      ctx.fill()
    })

    ctx.restore()
  }

  /**
   * 为卡片生成移动粒子的初始状态
   */
  generateParticles(x, y, width, height, particleConfig) {
    const particles = []

    for (let i = 0; i < particleConfig.count; i++) {
      let px, py
      let attempts = 0

      // 尝试在边框区域找到合适的位置
      do {
        px = x + Math.random() * width
        py = y + Math.random() * height
        attempts++
      } while (attempts < 50 && !(px < x + 30 || px > x + width - 30 || py < y + 30 || py > y + height - 30))

      particles.push({
        baseX: px,  // 基础X位置
        baseY: py,  // 基础Y位置
        size: particleConfig.minSize + Math.random() * (particleConfig.maxSize - particleConfig.minSize),
        // 移动相关属性
        offsetX: Math.random() * Math.PI * 2,  // X方向的相位偏移
        offsetY: Math.random() * Math.PI * 2,  // Y方向的相位偏移
        moveRangeX: 5 + Math.random() * 10,    // X方向移动范围（5-15px）
        moveRangeY: 5 + Math.random() * 10     // Y方向移动范围（5-15px）
      })
    }

    return particles
  }

  /**
   * 获取粒子配置
   */
  getParticleConfig(rarity) {
    const configs = {
      rare: {
        color: '#B0E0E6',
        count: 8,
        minSize: 1,
        maxSize: 3
      },
      epic: {
        color: '#C4B5FD',
        count: 12,
        minSize: 1,
        maxSize: 4
      },
      legendary: {
        color: '#FEF3C7',
        count: 15,
        minSize: 2,
        maxSize: 5
      }
    }

    return configs[rarity] || null
  }



  /**
   * 回退到代码绘制边框
   */
  drawFallbackBorder(ctx, x, y, width, height, rarity, isPressed) {
    // 简单的回退边框
    const colors = {
      common: '#8B7355',
      rare: '#4A90E2',
      epic: '#8B5CF6',
      legendary: '#F59E0B'
    }

    const color = colors[rarity] || colors.common
    ctx.strokeStyle = isPressed ? this.adjustColor(color, -30) : color
    ctx.lineWidth = 4
    ctx.strokeRect(x, y, width, height)
  }
  
  /**
   * 调整颜色亮度
   */
  adjustColor(color, amount) {
    const hex = color.replace('#', '')
    const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount))
    const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount))
    const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount))
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }
  
  /**
   * 检查是否已加载
   */
  isReady() {
    return this.isLoaded
  }
  
  /**
   * 获取加载状态
   */
  getLoadStatus() {
    const stats = imageManager.getCacheStats()
    return {
      isLoaded: this.isLoaded,
      ...stats
    }
  }
}

// 创建全局单例
const imageBorderRenderer = new ImageBorderRenderer()

// 导出模块
module.exports = imageBorderRenderer
