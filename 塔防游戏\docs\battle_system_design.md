# 塔防游戏战斗系统设计文档

## 🎯 设计目标

- 流畅的60FPS游戏体验
- 简单易懂的操作方式
- 丰富的策略深度
- 适配微信小游戏环境

## 🏗️ 系统架构

### 核心组件

1. **BattleScene** - 战斗场景主控制器
2. **EntityManager** - 实体管理器
3. **GameMap** - 游戏地图系统
4. **WaveManager** - 波次管理系统
5. **UIManager** - UI交互管理器

## 🎮 游戏实体设计

### 基础实体类 (BaseEntity)

```javascript
class BaseEntity {
  constructor(x, y) {
    this.id = generateId()
    this.x = x
    this.y = y
    this.width = 32
    this.height = 32
    this.isAlive = true
    this.type = 'entity'
  }
  
  update(deltaTime) {}
  render(ctx) {}
  destroy() {}
}
```

### 塔类型系统

#### 1. 基础塔 (BaseTower)
```javascript
class BaseTower extends BaseEntity {
  constructor(x, y, config) {
    super(x, y)
    this.type = 'tower'
    this.level = 1
    this.damage = config.damage
    this.range = config.range
    this.attackSpeed = config.attackSpeed
    this.cost = config.cost
    this.target = null
    this.lastAttackTime = 0
  }
}
```

#### 2. 塔类型定义
- **箭塔** - 基础远程攻击，攻击速度快
- **炮塔** - 高伤害，攻击速度慢，有溅射
- **魔法塔** - 魔法伤害，可减速敌人
- **冰塔** - 冰冻效果，减速敌人
- **毒塔** - 持续伤害，降低敌人防御

### 敌人类型系统

#### 1. 基础敌人 (BaseEnemy)
```javascript
class BaseEnemy extends BaseEntity {
  constructor(path, config) {
    super(path[0].x, path[0].y)
    this.type = 'enemy'
    this.maxHp = config.hp
    this.hp = config.hp
    this.speed = config.speed
    this.armor = config.armor
    this.reward = config.reward
    this.path = path
    this.pathIndex = 0
    this.effects = []
  }
}
```

#### 2. 敌人类型定义
- **小兵** - 血量低，速度中等，数量多
- **重甲兵** - 血量高，速度慢，高护甲
- **快速兵** - 血量中等，速度快
- **飞行兵** - 只能被特定塔攻击
- **BOSS** - 血量极高，特殊技能

### 子弹系统

#### 1. 基础子弹 (BaseBullet)
```javascript
class BaseBullet extends BaseEntity {
  constructor(x, y, target, config) {
    super(x, y)
    this.type = 'bullet'
    this.target = target
    this.damage = config.damage
    this.speed = config.speed
    this.effectType = config.effectType
  }
}
```

#### 2. 子弹类型
- **普通箭矢** - 直线飞行
- **炮弹** - 抛物线飞行，溅射伤害
- **魔法球** - 追踪目标
- **冰锥** - 冰冻效果
- **毒液** - 持续伤害

## 🗺️ 地图系统设计

### 网格系统
```javascript
class GameMap {
  constructor(width, height, gridSize) {
    this.width = width
    this.height = height
    this.gridSize = gridSize
    this.grid = []
    this.path = []
    this.spawnPoint = null
    this.endPoint = null
  }
  
  // 网格类型
  // 0: 可建造
  // 1: 路径
  // 2: 障碍物
  // 3: 起点
  // 4: 终点
}
```

### 路径系统
- 预定义路径点
- 路径验证机制
- 动态路径计算（高级功能）

## ⚔️ 战斗系统设计

### 攻击机制
1. **目标选择策略**
   - 最近敌人优先
   - 血量最少优先
   - 最强敌人优先

2. **伤害计算**
```javascript
function calculateDamage(attacker, target) {
  let damage = attacker.damage
  let armor = target.armor
  let finalDamage = Math.max(1, damage - armor)
  return finalDamage
}
```

3. **效果系统**
   - 减速效果
   - 中毒效果
   - 冰冻效果
   - 护甲削弱

### 碰撞检测
```javascript
function checkCollision(bullet, enemy) {
  let dx = bullet.x - enemy.x
  let dy = bullet.y - enemy.y
  let distance = Math.sqrt(dx * dx + dy * dy)
  return distance < (bullet.radius + enemy.radius)
}
```

## 🌊 波次系统设计

### 波次配置
```javascript
const waveConfig = {
  wave1: {
    enemies: [
      { type: 'soldier', count: 10, interval: 1000 },
      { type: 'archer', count: 5, interval: 2000 }
    ],
    reward: { gold: 100, exp: 50 }
  }
}
```

### 难度递增
- 敌人数量增加
- 敌人血量提升
- 新敌人类型出现
- 多路径同时进攻

## 💰 经济系统

### 资源类型
- **金币** - 建造和升级塔
- **经验** - 解锁新塔类型
- **宝石** - 特殊道具（可选）

### 收入来源
- 击杀敌人奖励
- 波次完成奖励
- 关卡完成奖励

## 🎨 UI交互设计

### 建造系统
1. 点击空地显示建造菜单
2. 选择塔类型
3. 确认建造（扣除金币）
4. 显示攻击范围

### 升级系统
1. 点击已建造的塔
2. 显示升级选项和费用
3. 确认升级
4. 属性提升动画

### 游戏控制
- 暂停/继续按钮
- 加速按钮（2x速度）
- 设置菜单
- 退出确认

## 📊 数据配置

### 塔配置表
```javascript
const towerConfigs = {
  arrow: {
    name: '箭塔',
    damage: [20, 30, 45],
    range: [80, 90, 100],
    attackSpeed: [1.0, 1.2, 1.5],
    cost: [50, 75, 100]
  }
}
```

### 敌人配置表
```javascript
const enemyConfigs = {
  soldier: {
    name: '士兵',
    hp: 100,
    speed: 50,
    armor: 0,
    reward: 10
  }
}
```

## 🚀 性能优化策略

### 对象池
- 子弹对象池
- 敌人对象池
- 特效对象池

### 渲染优化
- 视锥剔除
- 批量渲染
- 纹理合并

### 内存管理
- 及时清理死亡对象
- 避免内存泄漏
- 合理的垃圾回收

## 📱 微信小游戏适配

### 性能限制
- 内存限制：256MB
- 包体限制：4MB
- CPU性能有限

### 优化方案
- 简化视觉效果
- 减少同屏对象数量
- 优化算法复杂度
- 使用本地存储缓存数据

## 🎯 开发优先级

### 第一阶段（MVP）
1. 基础塔和敌人
2. 简单地图
3. 基础战斗逻辑
4. 简单UI

### 第二阶段
1. 多种塔类型
2. 多种敌人类型
3. 升级系统
4. 特效系统

### 第三阶段
1. 复杂地图
2. BOSS战
3. 技能系统
4. 成就系统
