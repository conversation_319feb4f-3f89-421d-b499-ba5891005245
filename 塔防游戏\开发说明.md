# 塔防游戏开发说明

## 项目概述

这是一个微信横版塔防小游戏项目，包含完整的登录页面、屏幕适配系统和基础游戏框架。

## 最新更新 (2025-07-28)

### 修复的问题
1. **Canvas未定义错误**: 修复了 `ReferenceError: Canvas is not defined` 错误
2. **模块系统**: 将ES6模块语法改为CommonJS，适配微信小游戏环境
3. **屏幕适配优化**: 使用 `windowWidth/windowHeight` 替代 `screenWidth/screenHeight`
4. **触摸事件**: 使用微信小游戏原生触摸API (`wx.onTouchStart` 等)
5. **坐标转换**: 优化触摸坐标转换，使用 `pageX/pageY` 替代 `clientX/clientY`

### 技术改进
- 简化了微信小游戏适配器
- 优化了横屏适配逻辑
- 改进了安全区域处理
- 增强了错误处理机制

## 主要功能

### 1. 登录页面
- 精美的渐变背景
- 粒子动画效果
- 响应式按钮交互
- 开始游戏、设置、关于功能

### 2. 屏幕适配系统
- 支持横屏模式
- 多分辨率自动适配
- 刘海屏检测和适配
- 安全区域计算
- 触摸坐标转换

### 3. 游戏框架
- 场景管理系统
- 游戏循环机制
- 生命周期管理
- 事件处理系统

## 技术特点

### 屏幕适配
- **设计基准**: 1334x750 (横屏)
- **适配方式**: 等比缩放 + 居中显示
- **坐标系统**: 设计坐标系自动转换为屏幕坐标系
- **触摸处理**: 自动转换触摸坐标到设计坐标系

### 性能优化
- Canvas 2D 渲染
- 请求动画帧优化
- 事件委托处理
- 内存管理

## 使用方法

### 1. 在微信开发者工具中打开
1. 打开微信开发者工具
2. 选择"小游戏"项目类型
3. 导入项目目录 `塔防游戏`
4. 点击"编译"运行

### 2. 项目结构说明
```
塔防游戏/
├── game.js                 # 游戏入口
├── game.json              # 游戏配置
├── project.config.json    # 项目配置
└── js/
    ├── main.js           # 主程序
    ├── scenes/           # 场景目录
    │   ├── LoginScene.js # 登录场景
    │   └── GameScene.js  # 游戏场景
    ├── utils/            # 工具类
    │   └── ScreenAdapter.js # 屏幕适配
    └── libs/             # 库文件
        ├── weapp-adapter.js # 微信适配器
        └── symbol.js        # Symbol polyfill
```

## 开发指南

### 添加新场景
1. 在 `js/scenes/` 目录下创建新场景类
2. 继承基础场景结构
3. 在 `main.js` 中注册场景
4. 实现场景切换逻辑

### 屏幕适配使用
```javascript
// 获取适配后的坐标
const pos = this.adapter.getAdaptedPosition(x, y)

// 获取适配后的尺寸
const size = this.adapter.getAdaptedSize(width, height)

// 获取适配后的字体大小
const fontSize = this.adapter.getAdaptedFontSize(20)
```

### 触摸事件处理
```javascript
// 获取设计坐标系下的触摸位置
getTouchPosition(touch) {
  const rect = this.canvas.getBoundingClientRect()
  const x = (touch.clientX - rect.left - this.adapter.offsetX) / this.adapter.scale
  const y = (touch.clientY - rect.top - this.adapter.offsetY) / this.adapter.scale
  return { x, y }
}
```

## 扩展建议

### 1. 游戏内容
- 添加塔防游戏逻辑
- 实现敌人AI系统
- 添加塔的建造和升级
- 实现关卡系统

### 2. UI优化
- 添加音效和背景音乐
- 实现更丰富的动画效果
- 添加设置页面
- 实现排行榜功能

### 3. 性能优化
- 对象池管理
- 纹理缓存
- 碰撞检测优化
- 渲染批处理

## 注意事项

1. **微信小游戏限制**
   - 包体大小限制 4MB
   - 内存使用限制
   - API调用频率限制

2. **性能考虑**
   - 避免频繁的DOM操作
   - 合理使用Canvas绘制
   - 注意内存泄漏

3. **兼容性**
   - 测试不同机型适配
   - 考虑低端设备性能
   - 处理网络异常情况

## 版本历史

- v1.0.0 (2025-07-28)
  - 初始版本发布
  - 登录页面实现
  - 屏幕适配系统
  - 基础游戏框架
