# UI组件目录

这个目录包含可复用的UI组件。

## 组件列表

### BackButton.js
统一的返回按钮组件，支持：
- 序列帧动画（8张图片）
- 静态备用方案
- 长方形设计（80x36像素）
- 统一的样式和交互

## 使用方法

```javascript
// 创建返回按钮实例
const backButton = new BackButton(imageManager)

// 在游戏循环中更新动画
backButton.update()

// 在渲染中绘制按钮
backButton.draw(ctx)

// 检查点击
if (backButton.isPointInButton(touchPos)) {
  backButton.setPressed(true)
}

// 处理点击事件
if (backButton.isPressed() && backButton.isPointInButton(touchPos)) {
  // 执行返回操作
  this.onBackClick()
}
```

## 设计原则

- **统一性**：所有页面使用相同的返回按钮样式
- **可复用**：一次实现，多处使用
- **可扩展**：支持序列帧动画和静态备用
- **易维护**：修改一处，全局生效
