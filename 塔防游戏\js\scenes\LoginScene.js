const ScreenAdapter = require('../utils/ScreenAdapter.js')

/**
 * 登录场景
 */
class LoginScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()

    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 背景颜色
    this.bgColor = '#1a1a2e'
    this.accentColor = '#16213e'
    this.primaryColor = '#0f3460'
    this.highlightColor = '#e94560'



    // 直接基于屏幕尺寸设置按钮位置
    this.setupButtons()

    // 动画相关
    this.animationTime = 0
    this.particles = []
    this.initParticles()

    // 绑定触摸事件
    this.bindEvents()
  }

  /**
   * 设置按钮
   */
  setupButtons() {
    const centerX = this.adapter.getWindowWidth() / 2
    const centerY = this.adapter.getWindowHeight() / 2

    this.buttons = {
      startGame: {
        text: '开始游戏',
        x: centerX,
        y: centerY - 50,
        width: 200,
        height: 60,
        isPressed: false,
        isHovered: false
      },
      settings: {
        text: '设置',
        x: centerX - 120,
        y: centerY + 50,
        width: 100,
        height: 50,
        isPressed: false,
        isHovered: false
      },
      about: {
        text: '关于',
        x: centerX + 120,
        y: centerY + 50,
        width: 100,
        height: 50,
        isPressed: false,
        isHovered: false
      }
    }
  }

  /**
   * 初始化粒子效果
   */
  initParticles() {
    for (let i = 0; i < 50; i++) {
      this.particles.push({
        x: Math.random() * this.adapter.getWindowWidth(),
        y: Math.random() * this.adapter.getWindowHeight(),
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.5 + 0.2
      })
    }
  }

  /**
   * 绑定触摸事件
   */
  bindEvents() {
    // 保存事件处理器的引用，以便后续清理
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
    wx.onTouchMove(this.touchMoveHandler)
  }
  

  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }  // 直接使用屏幕坐标

    // 检查按钮点击
    Object.keys(this.buttons).forEach(key => {
      const button = this.buttons[key]
      if (this.isPointInButton(pos, button)) {
        button.isPressed = true
      }
    })
  }

  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }  // 直接使用屏幕坐标

    // 检查按钮点击
    Object.keys(this.buttons).forEach(key => {
      const button = this.buttons[key]
      if (button.isPressed && this.isPointInButton(pos, button)) {
        this.onButtonClick(key)
      }
      button.isPressed = false
    })
  }

  /**
   * 触摸移动事件
   */
  onTouchMove(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }  // 直接使用屏幕坐标

    // 更新按钮悬停状态
    Object.keys(this.buttons).forEach(key => {
      const button = this.buttons[key]
      button.isHovered = this.isPointInButton(pos, button)
    })
  }
  

  
  /**
   * 检查点是否在按钮内
   */
  isPointInButton(pos, button) {
    return pos.x >= button.x - button.width / 2 &&
           pos.x <= button.x + button.width / 2 &&
           pos.y >= button.y - button.height / 2 &&
           pos.y <= button.y + button.height / 2
  }
  
  /**
   * 按钮点击处理
   */
  onButtonClick(buttonKey) {
    switch (buttonKey) {
      case 'startGame':
        wx.showToast({
          title: '进入游戏！',
          icon: 'success'
        })
        if (this.onSceneChange) {
          this.onSceneChange('game')
        }
        break
      case 'settings':
        if (this.onSceneChange) {
          this.onSceneChange('settings')
        }
        break
      case 'about':
        wx.showToast({
          title: '塔防英雄 v1.0.0',
          icon: 'none'
        })
        break
    }
  }

  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    this.animationTime += deltaTime

    // 更新粒子
    this.particles.forEach(particle => {
      particle.x += particle.vx
      particle.y += particle.vy

      // 边界检查
      if (particle.x < 0 || particle.x > this.adapter.getWindowWidth()) {
        particle.vx = -particle.vx
      }
      if (particle.y < 0 || particle.y > this.adapter.getWindowHeight()) {
        particle.vy = -particle.vy
      }
    })
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 应用像素比缩放
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 绘制背景
    this.drawBackground()

    // 绘制粒子效果
    this.drawParticles()

    // 绘制按钮
    this.drawButtons()

    // 绘制版本信息
    this.drawVersionInfo()

    this.ctx.restore()
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
    gradient.addColorStop(0, this.bgColor)
    gradient.addColorStop(0.5, this.accentColor)
    gradient.addColorStop(1, this.primaryColor)

    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
  }
  
  /**
   * 绘制粒子效果
   */
  drawParticles() {
    this.particles.forEach(particle => {
      this.ctx.save()
      this.ctx.globalAlpha = particle.opacity
      this.ctx.fillStyle = '#ffffff'
      this.ctx.beginPath()
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.restore()
    })
  }
  

  
  /**
   * 绘制按钮
   */
  drawButtons() {
    Object.keys(this.buttons).forEach(key => {
      const button = this.buttons[key]
      this.drawButton(button)
    })
  }
  
  /**
   * 绘制单个按钮
   */
  drawButton(button) {
    const { x, y, width, height, text, isPressed, isHovered } = button
    
    // 按钮背景
    let bgColor = this.highlightColor
    if (isPressed) {
      bgColor = '#c73650'
    } else if (isHovered) {
      bgColor = '#ff5577'
    }
    
    // 绘制按钮背景
    this.ctx.fillStyle = bgColor
    this.ctx.fillRect(x - width / 2, y - height / 2, width, height)
    
    // 绘制按钮边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x - width / 2, y - height / 2, width, height)
    
    // 绘制按钮文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(text, x, y)
  }
  
  /**
   * 绘制版本信息
   */
  drawVersionInfo() {
    this.ctx.fillStyle = '#666666'
    this.ctx.font = '16px Arial'
    this.ctx.textAlign = 'right'
    this.ctx.textBaseline = 'bottom'
    this.ctx.fillText('v1.0.0', this.adapter.getWindowWidth() - 20, this.adapter.getWindowHeight() - 20)
  }

  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }

  /**
   * 销毁场景
   */
  destroy() {
    // 清理触摸事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }
    if (this.touchMoveHandler) {
      wx.offTouchMove(this.touchMoveHandler)
    }

    // 清理其他资源
    this.touchStartHandler = null
    this.touchEndHandler = null
    this.touchMoveHandler = null
  }
}

// 导出模块
module.exports = LoginScene
