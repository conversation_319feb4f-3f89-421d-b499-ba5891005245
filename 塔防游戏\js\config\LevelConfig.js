/**
 * 关卡配置系统
 * 定义所有关卡的数据结构和配置
 */

/**
 * 关卡配置数据结构
 */
const LevelConfig = {
  
  /**
   * 关卡1 - 新手教学
   */
  level_1: {
    id: 1,
    name: "初次相遇",
    description: "学习基础的塔防操作",
    difficulty: "easy",
    
    // 地图配置
    map: {
      width: 800,
      height: 600,
      gridSize: 40,
      background: "grass",
      
      // 路径定义 (相对坐标，0-1之间)
      path: [
        { x: 0, y: 0.5 },      // 起点：左侧中央
        { x: 0.3, y: 0.5 },
        { x: 0.3, y: 0.2 },
        { x: 0.7, y: 0.2 },
        { x: 0.7, y: 0.8 },
        { x: 1, y: 0.8 }       // 终点：右侧下方
      ],
      
      // 可建造区域 (网格坐标)
      buildableAreas: [
        { x: 2, y: 4, width: 2, height: 2 },
        { x: 6, y: 2, width: 2, height: 2 },
        { x: 10, y: 6, width: 2, height: 2 }
      ]
    },
    
    // 波次配置
    waves: [
      {
        id: 1,
        delay: 2000,  // 波次开始延迟(ms)
        enemies: [
          {
            type: "soldier",
            count: 5,
            interval: 1500,  // 生成间隔(ms)
            hp: 80,
            speed: 40,
            armor: 0,
            reward: 10
          }
        ]
      },
      {
        id: 2,
        delay: 8000,
        enemies: [
          {
            type: "soldier",
            count: 8,
            interval: 1200,
            hp: 100,
            speed: 45,
            armor: 0,
            reward: 12
          }
        ]
      },
      {
        id: 3,
        delay: 15000,
        enemies: [
          {
            type: "soldier",
            count: 6,
            interval: 1000,
            hp: 120,
            speed: 50,
            armor: 5,
            reward: 15
          },
          {
            type: "archer",
            count: 3,
            interval: 2000,
            hp: 60,
            speed: 60,
            armor: 0,
            reward: 20
          }
        ]
      }
    ],
    
    // 初始资源
    initialResources: {
      gold: 200,
      lives: 20
    },
    
    // 胜利条件
    victory: {
      type: "survive_all_waves",
      description: "消灭所有敌人"
    },
    
    // 失败条件
    defeat: {
      type: "lives_depleted",
      description: "生命值归零"
    },
    
    // 奖励
    rewards: {
      gold: 150,
      exp: 100,
      stars: {
        1: { condition: "complete", description: "完成关卡" },
        2: { condition: "lives_above", value: 15, description: "生命值保持在15以上" },
        3: { condition: "lives_above", value: 18, description: "生命值保持在18以上" }
      }
    },
    
    // 推荐塔配置
    recommendedTowers: ["arrow_tower", "cannon_tower"]
  },

  /**
   * 关卡2 - 分叉路径
   */
  level_2: {
    id: 2,
    name: "分叉路径",
    description: "敌人会从两条路径进攻",
    difficulty: "easy",
    
    map: {
      width: 800,
      height: 600,
      gridSize: 40,
      background: "forest",
      
      // 双路径
      paths: [
        // 路径1：上方
        [
          { x: 0, y: 0.3 },
          { x: 0.4, y: 0.3 },
          { x: 0.6, y: 0.1 },
          { x: 1, y: 0.1 }
        ],
        // 路径2：下方
        [
          { x: 0, y: 0.7 },
          { x: 0.4, y: 0.7 },
          { x: 0.6, y: 0.9 },
          { x: 1, y: 0.9 }
        ]
      ],
      
      buildableAreas: [
        { x: 3, y: 2, width: 3, height: 2 },
        { x: 3, y: 8, width: 3, height: 2 },
        { x: 8, y: 4, width: 2, height: 4 }
      ]
    },
    
    waves: [
      {
        id: 1,
        delay: 3000,
        enemies: [
          {
            type: "soldier",
            count: 4,
            interval: 1500,
            path: 0,  // 使用第一条路径
            hp: 100,
            speed: 45,
            armor: 0,
            reward: 12
          },
          {
            type: "soldier",
            count: 4,
            interval: 1500,
            path: 1,  // 使用第二条路径
            hp: 100,
            speed: 45,
            armor: 0,
            reward: 12
          }
        ]
      },
      {
        id: 2,
        delay: 12000,
        enemies: [
          {
            type: "heavy_soldier",
            count: 3,
            interval: 2000,
            path: 0,
            hp: 200,
            speed: 30,
            armor: 10,
            reward: 25
          },
          {
            type: "archer",
            count: 6,
            interval: 1000,
            path: 1,
            hp: 80,
            speed: 55,
            armor: 0,
            reward: 18
          }
        ]
      }
    ],
    
    initialResources: {
      gold: 250,
      lives: 20
    },
    
    victory: {
      type: "survive_all_waves"
    },
    
    defeat: {
      type: "lives_depleted"
    },
    
    rewards: {
      gold: 200,
      exp: 150,
      stars: {
        1: { condition: "complete" },
        2: { condition: "lives_above", value: 12 },
        3: { condition: "no_tower_sold", description: "不出售任何塔" }
      }
    },
    
    recommendedTowers: ["arrow_tower", "cannon_tower", "magic_tower"]
  },

  /**
   * 关卡配置模板
   */
  template: {
    id: 0,
    name: "",
    description: "",
    difficulty: "easy", // easy, normal, hard, expert
    
    map: {
      width: 800,
      height: 600,
      gridSize: 40,
      background: "grass", // grass, forest, desert, snow, volcano
      path: [],
      paths: [], // 多路径
      buildableAreas: [],
      obstacles: [] // 障碍物
    },
    
    waves: [],
    
    initialResources: {
      gold: 200,
      lives: 20
    },
    
    victory: {
      type: "survive_all_waves" // survive_all_waves, time_limit, kill_count
    },
    
    defeat: {
      type: "lives_depleted" // lives_depleted, time_up
    },
    
    rewards: {
      gold: 0,
      exp: 0,
      stars: {
        1: { condition: "complete" },
        2: { condition: "lives_above", value: 15 },
        3: { condition: "perfect", description: "完美通关" }
      }
    },
    
    recommendedTowers: [],
    
    // 特殊规则
    specialRules: {
      towerLimit: null,     // 塔数量限制
      allowedTowers: null,  // 允许的塔类型
      timeLimit: null,      // 时间限制
      fogOfWar: false,      // 战争迷雾
      nightMode: false      // 夜晚模式
    }
  }
}

/**
 * 获取关卡配置
 */
function getLevelConfig(levelId) {
  const key = `level_${levelId}`
  return LevelConfig[key] || null
}

/**
 * 获取所有关卡列表
 */
function getAllLevels() {
  const levels = []
  for (let key in LevelConfig) {
    if (key.startsWith('level_')) {
      levels.push(LevelConfig[key])
    }
  }
  return levels.sort((a, b) => a.id - b.id)
}

/**
 * 验证关卡配置
 */
function validateLevelConfig(config) {
  const required = ['id', 'name', 'map', 'waves', 'initialResources']
  for (let field of required) {
    if (!config[field]) {
      throw new Error(`关卡配置缺少必要字段: ${field}`)
    }
  }
  return true
}

// 导出模块
module.exports = {
  LevelConfig,
  getLevelConfig,
  getAllLevels,
  validateLevelConfig
}
