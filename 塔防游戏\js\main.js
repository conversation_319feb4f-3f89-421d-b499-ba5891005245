// 先加载组件
const BackButton = require('./components/BackButton.js')

// 再加载场景
const LoginScene = require('./scenes/LoginScene.js')
const GameScene = require('./scenes/GameScene.js')
const ChallengeScene = require('./scenes/ChallengeScene.js')
const HeroScene = require('./scenes/HeroScene.js')
const AdventureMapScene = require('./scenes/AdventureMapScene.js')

/**
 * 游戏主类
 */
class Main {
  constructor() {
    // 获取画布
    this.canvas = wx.createCanvas()
    this.ctx = this.canvas.getContext('2d')
    
    // 设置画布尺寸
    this.setupCanvas()
    
    // 当前场景
    this.currentScene = null
    
    // 游戏循环相关
    this.lastTime = 0
    this.isRunning = false
    
    // 初始化游戏
    this.init()
  }
  
  /**
   * 设置画布尺寸
   */
  setupCanvas() {
    const systemInfo = wx.getSystemInfoSync()

    // 直接使用窗口尺寸，让微信小游戏自动处理横屏
    const canvasWidth = systemInfo.windowWidth
    const canvasHeight = systemInfo.windowHeight

    // 设置Canvas的实际像素尺寸（高分辨率支持）
    this.canvas.width = canvasWidth * systemInfo.pixelRatio
    this.canvas.height = canvasHeight * systemInfo.pixelRatio

    // 设置Canvas的显示尺寸
    if (this.canvas.style) {
      this.canvas.style.width = canvasWidth + 'px'
      this.canvas.style.height = canvasHeight + 'px'
    }

    // 设置Canvas上下文的缩放，确保绘制内容清晰
    this.ctx.scale(systemInfo.pixelRatio, systemInfo.pixelRatio)
  }
  
  /**
   * 初始化游戏
   */
  init() {
    // 创建登录场景
    this.currentScene = new LoginScene(this.canvas, this.ctx)
    this.currentScene.setSceneChangeCallback(this.onSceneChange.bind(this))

    // 开始游戏循环
    this.startGameLoop()

    // 监听小程序生命周期
    this.bindAppEvents()
  }

  /**
   * 场景切换处理
   */
  onSceneChange(sceneName) {

    switch (sceneName) {
      case 'login':
        this.switchScene(LoginScene)
        this.currentScene.setSceneChangeCallback(this.onSceneChange.bind(this))
        break
      case 'game':
        this.switchScene(GameScene)
        this.currentScene.setSceneChangeCallback(this.onSceneChange.bind(this))
        break
      case 'challenge':
        this.switchScene(ChallengeScene)
        this.currentScene.setSceneChangeCallback(this.onSceneChange.bind(this))
        break
      case 'hero':
        this.switchScene(HeroScene)
        this.currentScene.setSceneChangeCallback(this.onSceneChange.bind(this))
        break
      case 'adventure-map':
        this.switchScene(AdventureMapScene)
        this.currentScene.setSceneChangeCallback(this.onSceneChange.bind(this))
        break
      default:
        console.warn('未知场景:', sceneName)
    }
  }
  
  /**
   * 绑定小程序事件
   */
  bindAppEvents() {
    // 监听小程序显示
    wx.onShow(() => {
      this.resume()
    })

    // 监听小程序隐藏
    wx.onHide(() => {
      this.pause()
    })

    // 监听音频中断
    wx.onAudioInterruptionBegin(() => {
      this.pause()
    })

    // 监听音频中断结束
    wx.onAudioInterruptionEnd(() => {
      this.resume()
    })
  }
  
  /**
   * 开始游戏循环
   */
  startGameLoop() {
    this.isRunning = true
    this.lastTime = Date.now()
    this.gameLoop()
  }
  
  /**
   * 游戏循环
   */
  gameLoop() {
    if (!this.isRunning) return
    
    const currentTime = Date.now()
    const deltaTime = currentTime - this.lastTime
    this.lastTime = currentTime
    
    // 更新游戏逻辑
    this.update(deltaTime)
    
    // 渲染游戏画面
    this.render()
    
    // 请求下一帧
    requestAnimationFrame(() => this.gameLoop())
  }
  
  /**
   * 更新游戏逻辑
   */
  update(deltaTime) {
    if (this.currentScene && this.currentScene.update) {
      this.currentScene.update(deltaTime)
    }
  }
  
  /**
   * 渲染游戏画面
   */
  render() {
    if (this.currentScene && this.currentScene.render) {
      this.currentScene.render()
    }
  }
  
  /**
   * 暂停游戏
   */
  pause() {
    console.log('游戏暂停')
    this.isRunning = false
  }
  
  /**
   * 恢复游戏
   */
  resume() {
    console.log('游戏恢复')
    if (!this.isRunning) {
      this.isRunning = true
      this.lastTime = Date.now()
      this.gameLoop()
    }
  }
  
  /**
   * 切换场景
   */
  switchScene(SceneClass, ...args) {
    // 清理当前场景
    if (this.currentScene && this.currentScene.destroy) {
      this.currentScene.destroy()
    }

    // 创建新场景
    this.currentScene = new SceneClass(this.canvas, this.ctx, ...args)
  }
  
  /**
   * 销毁游戏
   */
  destroy() {
    this.isRunning = false
    
    if (this.currentScene && this.currentScene.destroy) {
      this.currentScene.destroy()
    }
    
    this.currentScene = null
  }
}

// 导出模块
module.exports = Main
