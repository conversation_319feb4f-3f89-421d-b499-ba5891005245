/**
 * 音频配置
 * 定义游戏中所有音效和音乐的配置
 */

const AudioConfig = {
  
  /**
   * 背景音乐配置
   */
  music: {
    menu_theme: {
      src: 'assets/audio/music/menu_theme.mp3',
      volume: 0.7,
      loop: true,
      description: '主菜单背景音乐'
    },
    
    battle_theme: {
      src: 'assets/audio/music/battle_theme.mp3',
      volume: 0.6,
      loop: true,
      description: '战斗背景音乐'
    },
    
    victory_theme: {
      src: 'assets/audio/music/victory.mp3',
      volume: 0.8,
      loop: false,
      description: '胜利音乐'
    },
    
    defeat_theme: {
      src: 'assets/audio/music/defeat.mp3',
      volume: 0.7,
      loop: false,
      description: '失败音乐'
    }
  },
  
  /**
   * 音效配置
   */
  sfx: {
    // UI音效
    button_click: {
      src: 'assets/audio/sfx/button_click.mp3',
      volume: 0.8,
      description: '按钮点击音效'
    },
    
    button_hover: {
      src: 'assets/audio/sfx/button_hover.mp3',
      volume: 0.6,
      description: '按钮悬停音效'
    },
    
    menu_open: {
      src: 'assets/audio/sfx/menu_open.mp3',
      volume: 0.7,
      description: '菜单打开音效'
    },
    
    menu_close: {
      src: 'assets/audio/sfx/menu_close.mp3',
      volume: 0.7,
      description: '菜单关闭音效'
    },
    
    // 建造音效
    build_tower: {
      src: 'assets/audio/sfx/build_tower.mp3',
      volume: 0.8,
      description: '建造塔音效'
    },
    
    tower_upgrade: {
      src: 'assets/audio/sfx/tower_upgrade.mp3',
      volume: 0.9,
      description: '塔升级音效'
    },
    
    tower_sell: {
      src: 'assets/audio/sfx/tower_sell.mp3',
      volume: 0.7,
      description: '塔出售音效'
    },
    
    // 攻击音效
    attack_arrow: {
      src: 'assets/audio/sfx/attack_arrow.mp3',
      volume: 0.6,
      description: '箭塔攻击音效'
    },
    
    attack_cannonball: {
      src: 'assets/audio/sfx/attack_cannon.mp3',
      volume: 0.8,
      description: '炮塔攻击音效'
    },
    
    attack_magic_bolt: {
      src: 'assets/audio/sfx/attack_magic.mp3',
      volume: 0.7,
      description: '魔法塔攻击音效'
    },
    
    attack_ice_shard: {
      src: 'assets/audio/sfx/attack_ice.mp3',
      volume: 0.7,
      description: '冰塔攻击音效'
    },
    
    attack_poison_dart: {
      src: 'assets/audio/sfx/attack_poison.mp3',
      volume: 0.6,
      description: '毒塔攻击音效'
    },
    
    // 命中音效
    hit: {
      src: 'assets/audio/sfx/hit.mp3',
      volume: 0.5,
      description: '命中音效'
    },
    
    hit_critical: {
      src: 'assets/audio/sfx/hit_critical.mp3',
      volume: 0.7,
      description: '暴击音效'
    },
    
    explosion: {
      src: 'assets/audio/sfx/explosion.mp3',
      volume: 0.8,
      description: '爆炸音效'
    },
    
    // 敌人音效
    enemy_spawn: {
      src: 'assets/audio/sfx/enemy_spawn.mp3',
      volume: 0.6,
      description: '敌人生成音效'
    },
    
    enemy_death: {
      src: 'assets/audio/sfx/enemy_death.mp3',
      volume: 0.7,
      description: '敌人死亡音效'
    },
    
    enemy_reach_end: {
      src: 'assets/audio/sfx/enemy_reach_end.mp3',
      volume: 0.8,
      description: '敌人到达终点音效'
    },
    
    // 特殊效果音效
    freeze: {
      src: 'assets/audio/sfx/freeze.mp3',
      volume: 0.6,
      description: '冰冻效果音效'
    },
    
    poison: {
      src: 'assets/audio/sfx/poison.mp3',
      volume: 0.5,
      description: '中毒效果音效'
    },
    
    slow: {
      src: 'assets/audio/sfx/slow.mp3',
      volume: 0.5,
      description: '减速效果音效'
    },
    
    // 游戏状态音效
    wave_start: {
      src: 'assets/audio/sfx/wave_start.mp3',
      volume: 0.8,
      description: '波次开始音效'
    },
    
    wave_complete: {
      src: 'assets/audio/sfx/wave_complete.mp3',
      volume: 0.8,
      description: '波次完成音效'
    },
    
    victory: {
      src: 'assets/audio/sfx/victory.mp3',
      volume: 0.9,
      description: '胜利音效'
    },
    
    defeat: {
      src: 'assets/audio/sfx/defeat.mp3',
      volume: 0.8,
      description: '失败音效'
    },
    
    life_lost: {
      src: 'assets/audio/sfx/life_lost.mp3',
      volume: 0.8,
      description: '生命值减少音效'
    },
    
    // 奖励音效
    coin_pickup: {
      src: 'assets/audio/sfx/coin_pickup.mp3',
      volume: 0.7,
      description: '金币获得音效'
    },
    
    exp_gain: {
      src: 'assets/audio/sfx/exp_gain.mp3',
      volume: 0.6,
      description: '经验获得音效'
    },
    
    level_up: {
      src: 'assets/audio/sfx/level_up.mp3',
      volume: 0.9,
      description: '升级音效'
    },
    
    star_earned: {
      src: 'assets/audio/sfx/star_earned.mp3',
      volume: 0.8,
      description: '获得星星音效'
    }
  },
  
  /**
   * 音效组合配置
   */
  combos: {
    // 连击音效
    combo_2: {
      sfx: 'hit',
      volume: 0.6,
      pitch: 1.1
    },
    
    combo_3: {
      sfx: 'hit',
      volume: 0.7,
      pitch: 1.2
    },
    
    combo_5: {
      sfx: 'hit_critical',
      volume: 0.8,
      pitch: 1.0
    }
  },
  
  /**
   * 环境音效配置
   */
  ambient: {
    wind: {
      src: 'assets/audio/ambient/wind.mp3',
      volume: 0.3,
      loop: true,
      description: '风声环境音'
    },
    
    forest: {
      src: 'assets/audio/ambient/forest.mp3',
      volume: 0.4,
      loop: true,
      description: '森林环境音'
    },
    
    desert: {
      src: 'assets/audio/ambient/desert.mp3',
      volume: 0.3,
      loop: true,
      description: '沙漠环境音'
    }
  }
}

/**
 * 获取音乐配置
 */
function getMusicConfig(musicId) {
  return AudioConfig.music[musicId] || null
}

/**
 * 获取音效配置
 */
function getSfxConfig(sfxId) {
  return AudioConfig.sfx[sfxId] || null
}

/**
 * 获取环境音配置
 */
function getAmbientConfig(ambientId) {
  return AudioConfig.ambient[ambientId] || null
}

/**
 * 获取所有音频文件列表（用于预加载）
 */
function getAllAudioFiles() {
  const files = []
  
  // 音乐文件
  for (const [id, config] of Object.entries(AudioConfig.music)) {
    files.push({
      id: id,
      src: config.src,
      type: 'music',
      config: config
    })
  }
  
  // 音效文件
  for (const [id, config] of Object.entries(AudioConfig.sfx)) {
    files.push({
      id: id,
      src: config.src,
      type: 'sfx',
      config: config
    })
  }
  
  // 环境音文件
  for (const [id, config] of Object.entries(AudioConfig.ambient)) {
    files.push({
      id: id,
      src: config.src,
      type: 'ambient',
      config: config
    })
  }
  
  return files
}

/**
 * 获取音频文件总数
 */
function getAudioFileCount() {
  return Object.keys(AudioConfig.music).length + 
         Object.keys(AudioConfig.sfx).length + 
         Object.keys(AudioConfig.ambient).length
}

// 导出模块
module.exports = {
  AudioConfig,
  getMusicConfig,
  getSfxConfig,
  getAmbientConfig,
  getAllAudioFiles,
  getAudioFileCount
}
