/**
 * 音频管理器
 * 管理游戏中的音效和背景音乐
 */

class AudioManager {
  constructor() {
    // 音频上下文
    this.audioContext = null
    this.masterVolume = 1.0
    this.musicVolume = 0.7
    this.sfxVolume = 0.8
    
    // 音频缓存
    this.audioCache = new Map()
    this.musicCache = new Map()
    
    // 当前播放的音乐
    this.currentMusic = null
    this.musicFadeInterval = null
    
    // 音效队列（防止同时播放太多音效）
    this.sfxQueue = []
    this.maxConcurrentSfx = 8
    this.activeSfx = []
    
    // 设置
    this.musicEnabled = true
    this.sfxEnabled = true
    
    // 初始化
    this.initialize()
  }
  
  /**
   * 初始化音频系统
   */
  initialize() {
    try {
      // 在微信小游戏中使用wx.createInnerAudioContext
      this.createAudioContext = () => {
        return wx.createInnerAudioContext()
      }

      // 预加载核心音效
      this.preloadCoreAudio()

      console.log('音频管理器初始化成功')
    } catch (error) {
      console.warn('音频系统初始化失败:', error)
    }
  }

  /**
   * 预加载核心音效
   */
  preloadCoreAudio() {
    try {
      const AudioConfig = require('../config/AudioConfig.js')

      // 预加载常用音效
      const coreAudio = [
        'button_click',
        'build_tower',
        'attack_arrow',
        'hit',
        'enemy_death',
        'victory',
        'defeat'
      ]

      coreAudio.forEach(audioId => {
        const config = AudioConfig.getSfxConfig(audioId)
        if (config) {
          this.preloadAudio(audioId, config.src, false).catch(error => {
            console.warn(`音效预加载失败: ${audioId}`, error)
          })
        }
      })

      // 预加载背景音乐
      const coreMusic = ['battle_theme']
      coreMusic.forEach(musicId => {
        const config = AudioConfig.getMusicConfig(musicId)
        if (config) {
          this.preloadAudio(musicId, config.src, true).catch(error => {
            console.warn(`音乐预加载失败: ${musicId}`, error)
          })
        }
      })
    } catch (error) {
      console.warn('音频预加载初始化失败:', error)
    }
  }
  
  /**
   * 预加载音频文件
   * @param {string} id - 音频ID
   * @param {string} src - 音频文件路径
   * @param {boolean} isMusic - 是否为音乐文件
   */
  preloadAudio(id, src, isMusic = false) {
    return new Promise((resolve, reject) => {
      try {
        const audio = this.createAudioContext()
        audio.src = src
        
        audio.onCanplay = () => {
          if (isMusic) {
            this.musicCache.set(id, { audio, src })
          } else {
            this.audioCache.set(id, { audio, src })
          }
          resolve(audio)
        }
        
        audio.onError = (error) => {
          console.warn(`音频加载失败: ${id}`, error)
          reject(error)
        }
        
      } catch (error) {
        console.warn(`音频创建失败: ${id}`, error)
        reject(error)
      }
    })
  }
  
  /**
   * 播放音效
   * @param {string} id - 音效ID
   * @param {Object} options - 播放选项
   */
  playSfx(id, options = {}) {
    if (!this.sfxEnabled) return

    // 检查并发音效数量
    if (this.activeSfx.length >= this.maxConcurrentSfx) {
      return
    }

    try {
      // 创建新的音频实例（避免冲突）
      const audio = this.createAudioContext()
      const cached = this.audioCache.get(id)

      if (cached) {
        audio.src = cached.src
      } else {
        // 如果没有预加载，尝试直接加载
        audio.src = options.src || `assets/audio/sfx/${id}.mp3`
      }

      // 设置音量
      audio.volume = (options.volume || 1) * this.sfxVolume * this.masterVolume

      // 设置循环
      audio.loop = options.loop || false

      // 播放结束后清理
      audio.onEnded = () => {
        this.removeSfx(audio)
      }

      audio.onError = (error) => {
        // 静默处理音频加载错误，不影响游戏流程
        this.removeSfx(audio)
      }

      // 播放
      audio.play()

      // 添加到活跃列表
      this.activeSfx.push(audio)

      return audio

    } catch (error) {
      // 静默处理播放错误
      console.debug(`音效播放失败: ${id}`, error)
    }
  }
  
  /**
   * 播放背景音乐
   * @param {string} id - 音乐ID
   * @param {Object} options - 播放选项
   */
  playMusic(id, options = {}) {
    if (!this.musicEnabled) return

    try {
      // 停止当前音乐
      this.stopMusic()

      // 创建新的音乐实例
      const audio = this.createAudioContext()
      const cached = this.musicCache.get(id)

      if (cached) {
        audio.src = cached.src
      } else {
        audio.src = options.src || `assets/audio/music/${id}.mp3`
      }

      // 设置属性
      audio.volume = (options.volume || 1) * this.musicVolume * this.masterVolume
      audio.loop = options.loop !== false  // 默认循环

      // 错误处理
      audio.onError = (error) => {
        console.debug(`音乐加载失败: ${id}`, error)
        this.currentMusic = null
      }

      // 播放
      audio.play()

      this.currentMusic = audio

      // 淡入效果
      if (options.fadeIn) {
        this.fadeInMusic(options.fadeIn)
      }

      return audio

    } catch (error) {
      console.debug(`音乐播放失败: ${id}`, error)
    }
  }
  
  /**
   * 停止背景音乐
   * @param {number} fadeOut - 淡出时间（毫秒）
   */
  stopMusic(fadeOut = 0) {
    if (!this.currentMusic) return
    
    if (fadeOut > 0) {
      this.fadeOutMusic(fadeOut, () => {
        if (this.currentMusic) {
          this.currentMusic.stop()
          this.currentMusic = null
        }
      })
    } else {
      this.currentMusic.stop()
      this.currentMusic = null
    }
  }
  
  /**
   * 暂停背景音乐
   */
  pauseMusic() {
    if (this.currentMusic) {
      this.currentMusic.pause()
    }
  }
  
  /**
   * 恢复背景音乐
   */
  resumeMusic() {
    if (this.currentMusic) {
      this.currentMusic.play()
    }
  }
  
  /**
   * 音乐淡入
   */
  fadeInMusic(duration) {
    if (!this.currentMusic) return
    
    const startVolume = 0
    const targetVolume = this.musicVolume * this.masterVolume
    const steps = 20
    const stepDuration = duration / steps
    const volumeStep = (targetVolume - startVolume) / steps
    
    this.currentMusic.volume = startVolume
    
    let currentStep = 0
    const fadeInterval = setInterval(() => {
      currentStep++
      const newVolume = startVolume + volumeStep * currentStep
      
      if (this.currentMusic) {
        this.currentMusic.volume = Math.min(newVolume, targetVolume)
      }
      
      if (currentStep >= steps) {
        clearInterval(fadeInterval)
      }
    }, stepDuration)
  }
  
  /**
   * 音乐淡出
   */
  fadeOutMusic(duration, callback) {
    if (!this.currentMusic) return
    
    const startVolume = this.currentMusic.volume
    const steps = 20
    const stepDuration = duration / steps
    const volumeStep = startVolume / steps
    
    let currentStep = 0
    const fadeInterval = setInterval(() => {
      currentStep++
      const newVolume = startVolume - volumeStep * currentStep
      
      if (this.currentMusic) {
        this.currentMusic.volume = Math.max(newVolume, 0)
      }
      
      if (currentStep >= steps) {
        clearInterval(fadeInterval)
        if (callback) callback()
      }
    }, stepDuration)
  }
  
  /**
   * 移除音效
   */
  removeSfx(audio) {
    const index = this.activeSfx.indexOf(audio)
    if (index >= 0) {
      this.activeSfx.splice(index, 1)
    }
    
    try {
      audio.destroy()
    } catch (error) {
      // 忽略销毁错误
    }
  }
  
  /**
   * 停止所有音效
   */
  stopAllSfx() {
    for (const audio of this.activeSfx) {
      try {
        audio.stop()
        audio.destroy()
      } catch (error) {
        // 忽略错误
      }
    }
    this.activeSfx = []
  }
  
  /**
   * 设置主音量
   */
  setMasterVolume(volume) {
    this.masterVolume = Math.max(0, Math.min(1, volume))
    
    // 更新当前音乐音量
    if (this.currentMusic) {
      this.currentMusic.volume = this.musicVolume * this.masterVolume
    }
  }
  
  /**
   * 设置音乐音量
   */
  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume))
    
    if (this.currentMusic) {
      this.currentMusic.volume = this.musicVolume * this.masterVolume
    }
  }
  
  /**
   * 设置音效音量
   */
  setSfxVolume(volume) {
    this.sfxVolume = Math.max(0, Math.min(1, volume))
  }
  
  /**
   * 启用/禁用音乐
   */
  setMusicEnabled(enabled) {
    this.musicEnabled = enabled
    
    if (!enabled && this.currentMusic) {
      this.stopMusic()
    }
  }
  
  /**
   * 启用/禁用音效
   */
  setSfxEnabled(enabled) {
    this.sfxEnabled = enabled
    
    if (!enabled) {
      this.stopAllSfx()
    }
  }
  
  /**
   * 获取音频状态
   */
  getStatus() {
    return {
      musicEnabled: this.musicEnabled,
      sfxEnabled: this.sfxEnabled,
      masterVolume: this.masterVolume,
      musicVolume: this.musicVolume,
      sfxVolume: this.sfxVolume,
      currentMusic: this.currentMusic ? 'playing' : 'stopped',
      activeSfxCount: this.activeSfx.length
    }
  }
  
  /**
   * 清理资源
   */
  destroy() {
    this.stopMusic()
    this.stopAllSfx()
    
    // 清理缓存
    for (const [id, cached] of this.audioCache) {
      try {
        cached.audio.destroy()
      } catch (error) {
        // 忽略错误
      }
    }
    
    for (const [id, cached] of this.musicCache) {
      try {
        cached.audio.destroy()
      } catch (error) {
        // 忽略错误
      }
    }
    
    this.audioCache.clear()
    this.musicCache.clear()
  }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager()

// 导出模块
module.exports = audioManager
