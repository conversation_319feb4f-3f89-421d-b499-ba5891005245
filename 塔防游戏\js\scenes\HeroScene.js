const ScreenAdapter = require('../utils/ScreenAdapter.js')
const imageBorderRenderer = require('../utils/ImageBorderRenderer.js')
const starRenderer = require('../utils/StarRenderer.js')
const imageManager = require('../utils/ImageManager.js')
const BackButton = require('../components/BackButton.js')

// 边框宽度配置 - 根据实际边框图片调整
const BORDER_WIDTH = 30
// 内容区域偏移配置 - 适应不规则边框
const CONTENT_OFFSET_X = 5   // 内容区域向左偏移量
const CONTENT_OFFSET_Y_TOP = 30  // 内容区域顶部向下缩进量
const CONTENT_OFFSET_Y_BOTTOM = 10  // 内容区域底部向上缩进量

/**
 * 英雄页面场景
 */
class HeroScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 页面状态
    this.scrollX = 0  // 水平滚动位置
    this.maxScrollX = 0  // 最大水平滚动距离
    this.currentIndex = 0  // 当前显示的英雄索引
    this.currentFilter = 'all'  // 当前筛选条件

    // 创建统一的返回按钮组件
    this.backButtonComponent = new BackButton(imageManager)

    // 设置UI元素
    this.setupUI()
    
    // 绑定触摸事件
    this.bindEvents()

    // 初始化图片边框渲染器
    this.initializeBorderRenderer()
  }
  
  /**
   * 设置UI元素
   */
  setupUI() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()
    
    // 返回按钮现在使用统一组件 this.backButtonComponent
    
    // 左侧导航栏配置
    this.setupLeftNavigation()

    // 英雄卡片配置 - 9:16比例，自适应屏幕高度
    const leftNavWidth = 20 // 左侧导航栏宽度（大幅减少，让卡牌往前移动）
    const sideMargin = 20    // 减少边距
    const cardSpacing = 10   // 卡片之间的间距（减少到10px）

    // 计算合适的卡片尺寸，确保有预留空间
    const topMargin = 0     // 顶部预留空间
    const bottomMargin = 0  // 底部预留空间
    const availableHeight = windowHeight - topMargin - bottomMargin

    // 根据可用高度计算卡片尺寸，保持9:16比例
    const maxCardHeight = Math.min(availableHeight, 420) // 限制最大高度
    const cardHeight = maxCardHeight
    const cardWidth = Math.round(cardHeight * 9 / 16)    // 保持9:16比例

    const centerY = topMargin + availableHeight / 2  // 在可用空间内垂直居中

    // 显示实际卡片尺寸信息
    console.log(`卡片实际尺寸: ${cardWidth}x${cardHeight}px`)
    console.log(`请将边框图片制作为: ${cardWidth}x${cardHeight}px`)

    // 创建15个英雄卡片占位
    this.heroCards = []
    for (let i = 0; i < 15; i++) {
      const card = {
        id: i + 1,
        name: `英雄${i + 1}`,
        level: 1,
        rarity: this.getRandomRarity(),
        heroClass: this.getRandomHeroClass(), // 添加英雄职业
        starLevel: this.getRandomStarLevel(), // 添加星级属性
        isLocked: false, // 所有英雄都解锁
        x: leftNavWidth + sideMargin + cardWidth / 2 + i * (cardWidth + cardSpacing),
        y: centerY,
        width: cardWidth,
        height: cardHeight,
        isPressed: false
      }

      this.heroCards.push(card)
    }

    // 计算最大水平滚动距离
    this.updateScrollRange()

  }

  /**
   * 设置左侧导航栏
   */
  setupLeftNavigation() {
    // 导航栏状态
    this.navExpanded = false  // 默认收起状态
    this.navAnimating = false
    this.navAnimationProgress = 0  // 0=完全收起, 1=完全展开

    // 胶囊式导航栏配置
    this.navConfig = {
      buttonWidth: 55,      // 长方形按钮宽度
      buttonHeight: 45,     // 长方形按钮高度
      buttonSpacing: 5,     // 按钮间距（更紧凑）
      leftMargin: 15,       // 距离左边距离
      bottomMargin: 60      // 距离底部距离
    }

    // 计算导航栏位置（左侧中下部）
    const windowHeight = this.adapter.getWindowHeight()

    // 切换按钮（长方形，更靠近左下角）
    this.navToggleButton = {
      x: this.navConfig.leftMargin + this.navConfig.buttonWidth / 2,
      y: windowHeight - 25,  // 更靠近底部
      width: this.navConfig.buttonWidth,
      height: 35,  // 长方形高度
      isPressed: false
    }

    // 导航按钮配置（长方形按钮，显示文字）
    this.leftNavButtons = [
      {
        id: 'all',
        text: '全部',
        isActive: true
      },
      {
        id: 'warrior',
        text: '战士',
        isActive: false
      },
      {
        id: 'mage',
        text: '法师',
        isActive: false
      },
      {
        id: 'archer',
        text: '射手',
        isActive: false
      },
      {
        id: 'support',
        text: '辅助',
        isActive: false
      }
    ]

    // 计算按钮位置
    this.updateNavButtonPositions()
  }



  /**
   * 更新导航按钮位置（垂直长方形排列）
   */
  updateNavButtonPositions() {
    const windowHeight = this.adapter.getWindowHeight()
    const centerX = this.navConfig.leftMargin + this.navConfig.buttonWidth / 2

    // 计算按钮的垂直位置（从切换按钮上方开始）
    const toggleButtonY = windowHeight - 25
    const startY = toggleButtonY - 45  // 切换按钮上方45px开始

    // 反向排列，让"全部"在最上面
    this.leftNavButtons.forEach((button, index) => {
      const reverseIndex = this.leftNavButtons.length - 1 - index  // 反向索引
      const targetY = startY - reverseIndex * (this.navConfig.buttonHeight + this.navConfig.buttonSpacing)

      // 根据展开状态计算实际位置
      button.x = centerX
      button.y = this.navExpanded ? targetY : toggleButtonY  // 收起时都在切换按钮位置
      button.width = this.navConfig.buttonWidth
      button.height = this.navConfig.buttonHeight
      button.isPressed = false

      // 透明度根据展开状态和位置计算
      button.alpha = this.navExpanded ? this.navAnimationProgress : 0
    })
  }

  /**
   * 获取当前导航栏宽度（长方形固定宽度）
   */
  getCurrentNavWidth() {
    return this.navConfig.buttonWidth
  }

  /**
   * 获取当前导航栏X位置（固定位置）
   */
  getCurrentNavX() {
    return this.navConfig.leftMargin
  }

  /**
   * 切换导航栏展开/收起状态
   */
  toggleNavigation() {
    if (this.navAnimating) return

    this.navExpanded = !this.navExpanded
    this.navAnimating = true

    // 展开导航栏时自动选择"全部"
    if (this.navExpanded) {
      this.leftNavButtons.forEach(button => {
        button.isActive = button.id === 'all'  // 只有"全部"按钮激活
      })

      // 更新筛选条件，显示所有英雄
      this.currentFilter = 'all'

      // 重置滚动位置到第一个英雄
      this.scrollX = 0
      this.currentIndex = 0

      // 更新滚动范围
      this.updateScrollRange()

      console.log('导航栏展开，自动选择"全部"')
    } else {
      console.log('导航栏收起')
    }
  }

  /**
   * 更新导航栏动画（垂直展开效果）
   */
  updateNavigationAnimation() {
    if (!this.navAnimating) return

    const animationSpeed = 0.12  // 稍快的动画速度
    const targetProgress = this.navExpanded ? 1 : 0

    if (Math.abs(this.navAnimationProgress - targetProgress) < 0.01) {
      this.navAnimationProgress = targetProgress
      this.navAnimating = false
    } else {
      this.navAnimationProgress += (targetProgress - this.navAnimationProgress) * animationSpeed
    }

    // 更新按钮位置和透明度
    this.updateNavButtonPositions()
  }

  /**
   * 获取随机稀有度 - 3种品质：蓝紫红
   */
  getRandomRarity() {
    const rarities = ['rare', 'epic', 'legendary']  // 蓝色、紫色、红色
    const weights = [60, 30, 10] // 权重：稀有60%，史诗30%，传说10%

    const random = Math.random() * 100
    let cumulative = 0

    for (let i = 0; i < rarities.length; i++) {
      cumulative += weights[i]
      if (random <= cumulative) {
        return rarities[i]
      }
    }

    return 'rare'  // 默认返回稀有
  }

  /**
   * 获取随机英雄职业
   */
  getRandomHeroClass() {
    const classes = ['warrior', 'mage', 'archer', 'support']
    const weights = [30, 25, 25, 20] // 权重：战士30%，法师25%，射手25%，辅助20%

    const random = Math.random() * 100
    let cumulative = 0

    for (let i = 0; i < classes.length; i++) {
      cumulative += weights[i]
      if (random <= cumulative) {
        return classes[i]
      }
    }

    return 'warrior'
  }

  /**
   * 获取随机星级 (1-15)
   */
  getRandomStarLevel() {
    return Math.floor(Math.random() * 15) + 1
  }

  /**
   * 获取稀有度颜色 - 3种品质：重新设计的美观颜色
   */
  getRarityColor(rarity) {
    const colors = {
      rare: '#4A90E2',      // 优雅蓝色 - 更柔和的蓝色调
      epic: '#8E44AD',      // 深邃紫色 - 更深沉的紫色调
      legendary: '#E67E22'  // 华丽橙金色 - 温暖的橙金色调
    }
    return colors[rarity] || colors.rare  // 默认返回蓝色
  }
  
  /**
   * 初始化图片边框渲染器
   */
  async initializeBorderRenderer() {
    try {
      await imageBorderRenderer.initialize()
      console.log('图片边框渲染器初始化成功')
    } catch (error) {
      console.warn('图片边框渲染器初始化失败，将使用代码绘制:', error)
    }

    // 同时初始化星级渲染器
    try {
      await starRenderer.initialize()
      console.log('星级渲染器初始化成功')
    } catch (error) {
      console.warn('星级渲染器初始化失败，将使用文字绘制:', error)
    }

    // 初始化英雄背景图片容器
    this.heroBgImages = {}

    // 预加载所有英雄的背景图片
    for (let i = 1; i <= this.heroCards.length; i++) {
      try {
        this.heroBgImages[i] = await imageManager.loadHeroBgImage(i)
        console.log(`英雄${i}背景图片加载成功`)
      } catch (error) {
        console.warn(`英雄${i}背景图片加载失败:`, error)
        this.heroBgImages[i] = null
      }
    }

    // 预加载旋转特效图片
    try {
      this.rotationEffectImage = await imageManager.loadEffectImage('rotation')
      console.log('正向旋转特效图片加载成功')
    } catch (error) {
      console.warn('正向旋转特效图片加载失败:', error)
      this.rotationEffectImage = null
    }

    // 预加载反向旋转特效图片
    try {
      this.rotationEffectReverseImage = await imageManager.loadEffectImage('rotationReverse')
      console.log('反向旋转特效图片加载成功')
    } catch (error) {
      console.warn('反向旋转特效图片加载失败:', error)
      this.rotationEffectReverseImage = null
    }

    // 预加载若隐若现特效图片
    try {
      this.fadeEffectImage = await imageManager.loadEffectImage('fade')
      console.log('若隐若现特效图片加载成功')
    } catch (error) {
      console.warn('若隐若现特效图片加载失败:', error)
      this.fadeEffectImage = null
    }

    // 预加载英雄页面背景图片
    try {
      this.heroSceneBgImage = await imageManager.loadBackgroundImage('heroScene')
      console.log('英雄页面背景图片加载成功')
    } catch (error) {
      console.warn('英雄页面背景图片加载失败:', error)
      this.heroSceneBgImage = null
    }

    // 预加载序列帧图片
    this.sequenceFrames = {
      rare: [],
      epic: [],
      legendary: []
    }
    const qualities = ['rare', 'epic', 'legendary']

    for (const quality of qualities) {
      try {
        this.sequenceFrames[quality] = await imageManager.loadSequenceFrames(quality)
        console.log(`${quality}品质序列帧加载成功，共${this.sequenceFrames[quality].length}帧`)
      } catch (error) {
        console.warn(`${quality}品质序列帧加载失败:`, error)
        this.sequenceFrames[quality] = []
      }
    }

    // 预加载返回按钮序列帧
    try {
      this.backButtonFrames = await imageManager.loadBackButtonFrames()
      console.log(`返回按钮序列帧加载成功，共${this.backButtonFrames.length}帧`)
    } catch (error) {
      console.warn('返回按钮序列帧加载失败:', error)
      this.backButtonFrames = []
    }

    // 初始化动画参数
    this.rotationAngle = 0
    this.fadeTime = 0  // 若隐若现动画时间
    this.sequenceFrameIndex = 0  // 序列帧当前索引
    this.sequenceFrameTimer = 0  // 序列帧计时器
    this.backButtonFrameIndex = 0  // 返回按钮序列帧当前索引
    this.backButtonFrameTimer = 0  // 返回按钮序列帧计时器
  }

  /**
   * 绑定触摸事件
   */
  bindEvents() {
    // 保存事件处理器的引用，以便后续清理
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
    wx.onTouchMove(this.touchMoveHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    this.lastTouchX = pos.x
    this.lastTouchY = pos.y
    this.isDragging = false
    this.startTouchX = pos.x

    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
    }

    // 检查导航栏切换按钮
    if (this.isPointInCircleButton(pos, this.navToggleButton)) {
      this.navToggleButton.isPressed = true
    }

    // 检查左侧导航按钮
    this.leftNavButtons.forEach(button => {
      if (this.isPointInButton(pos, button)) {
        button.isPressed = true
      }
    })

    // 检查英雄卡片（只检查筛选后的英雄）
    const filteredHeroes = this.getFilteredHeroes()
    const leftNavWidth = 100
    const sideMargin = 20
    const cardSpacing = 10
    const centerY = this.heroCards[0].y

    filteredHeroes.forEach((card, index) => {
      const displayCard = {
        ...card,
        x: leftNavWidth + sideMargin + card.width / 2 + index * (card.width + cardSpacing),
        y: centerY
      }

      const adjustedPos = { x: pos.x + this.scrollX, y: pos.y }
      if (this.isPointInCard(adjustedPos, displayCard)) {
        card.isPressed = true
      }
    })
  }
  
  /**
   * 触摸移动事件
   */
  onTouchMove(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    if (this.lastTouchX !== undefined) {
      const deltaX = this.lastTouchX - pos.x

      if (Math.abs(deltaX) > 5) {
        this.isDragging = true

        // 更新水平滚动位置
        this.scrollX = Math.max(0, Math.min(this.maxScrollX, this.scrollX + deltaX))

        // 取消所有按钮按压状态
        this.backButtonComponent.setPressed(false)
        this.leftNavButtons.forEach(button => {
          button.isPressed = false
        })
        this.heroCards.forEach(card => {
          card.isPressed = false
        })
      }
    }

    this.lastTouchX = pos.x
    this.lastTouchY = pos.y
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    if (!this.isDragging) {
      // 检查返回按钮点击（优先级最高）
      if (this.backButtonComponent.isPressed() && this.backButtonComponent.isPointInButton(pos)) {
        this.onBackClick()
        this.resetButtonStates()  // 重置按钮状态
        return  // 点击返回按钮后立即返回，不处理其他点击
      }

      // 检查导航栏切换按钮点击
      if (this.navToggleButton.isPressed && this.isPointInCircleButton(pos, this.navToggleButton)) {
        this.toggleNavigation()
        this.resetButtonStates()  // 重置按钮状态
        return  // 点击导航按钮后立即返回，不处理其他点击
      }

      // 检查左侧导航按钮点击
      let navButtonClicked = false
      this.leftNavButtons.forEach(button => {
        if (button.isPressed && this.isPointInButton(pos, button)) {
          this.onNavButtonClick(button)
          navButtonClicked = true
        }
      })

      if (navButtonClicked) {
        this.resetButtonStates()  // 重置按钮状态
        return  // 点击导航按钮后立即返回，不处理卡片点击
      }

      // 检查英雄卡片点击（只检查筛选后的英雄）
      const filteredHeroes = this.getFilteredHeroes()
      const leftNavWidth = 100
      const sideMargin = 20
      const cardSpacing = 10
      const centerY = this.heroCards[0].y

      filteredHeroes.forEach((card, index) => {
        const displayCard = {
          ...card,
          x: leftNavWidth + sideMargin + card.width / 2 + index * (card.width + cardSpacing),
          y: centerY
        }

        const adjustedPos = { x: pos.x + this.scrollX, y: pos.y }
        if (card.isPressed && this.isPointInCard(adjustedPos, displayCard)) {
          this.onHeroClick(card)
        }
      })
    } else {
      // 滑动结束后的自动对齐
      this.snapToNearestCard()
    }

    // 重置状态
    this.resetButtonStates()
    this.isDragging = false
    this.lastTouchX = undefined
    this.lastTouchY = undefined
    this.startTouchX = undefined
  }

  /**
   * 重置所有按钮的按压状态
   */
  resetButtonStates() {
    this.backButtonComponent.setPressed(false)
    this.navToggleButton.isPressed = false
    this.leftNavButtons.forEach(button => {
      button.isPressed = false
    })
    this.heroCards.forEach(card => {
      card.isPressed = false
    })
  }

  /**
   * 自动对齐到最近的卡片
   */
  snapToNearestCard() {
    const filteredHeroes = this.getFilteredHeroes()
    if (filteredHeroes.length === 0) return

    // 使用第一个卡片的宽度和间距
    const cardWidth = filteredHeroes[0].width
    const cardSpacing = 10  // 更新卡片间距
    const cardTotalWidth = cardWidth + cardSpacing

    // 计算最接近的卡片索引
    const targetIndex = Math.round(this.scrollX / cardTotalWidth)
    const clampedIndex = Math.max(0, Math.min(filteredHeroes.length - 1, targetIndex))

    // 计算目标滚动位置
    const targetScrollX = clampedIndex * cardTotalWidth
    const clampedScrollX = Math.max(0, Math.min(this.maxScrollX, targetScrollX))

    // 平滑滚动到目标位置
    this.smoothScrollTo(clampedScrollX)
    this.currentIndex = clampedIndex
  }

  /**
   * 平滑滚动到目标位置
   */
  smoothScrollTo(targetX) {
    const startX = this.scrollX
    const distance = targetX - startX
    const duration = 300 // 毫秒
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // 使用缓动函数
      const easeProgress = this.easeOutCubic(progress)
      this.scrollX = startX + distance * easeProgress

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    animate()
  }

  /**
   * 缓动函数
   */
  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3)
  }

  /**
   * 检查点是否在按钮内
   */
  isPointInButton(pos, button) {
    return pos.x >= button.x - button.width / 2 &&
           pos.x <= button.x + button.width / 2 &&
           pos.y >= button.y - button.height / 2 &&
           pos.y <= button.y + button.height / 2
  }

  /**
   * 检查点是否在圆形按钮内
   */
  isPointInCircleButton(point, button) {
    const { x, y, width } = button
    const radius = width / 2
    const distance = Math.sqrt((point.x - x) ** 2 + (point.y - y) ** 2)
    return distance <= radius
  }
  
  /**
   * 检查点是否在卡片内
   */
  isPointInCard(pos, card) {
    return pos.x >= card.x - card.width / 2 &&
           pos.x <= card.x + card.width / 2 &&
           pos.y >= card.y - card.height / 2 &&
           pos.y <= card.y + card.height / 2
  }
  
  /**
   * 返回按钮点击
   */
  onBackClick() {
    if (this.onSceneChange) {
      this.onSceneChange('game')
    }
  }
  
  /**
   * 导航按钮点击
   */
  onNavButtonClick(button) {
    // 更新按钮状态
    this.leftNavButtons.forEach(btn => {
      btn.isActive = btn.id === button.id
    })

    // 更新筛选条件
    this.currentFilter = button.id

    // 重置滚动位置到第一个英雄
    this.scrollX = 0
    this.currentIndex = 0

    // 更新滚动范围
    this.updateScrollRange()
  }

  /**
   * 英雄卡片点击
   */
  onHeroClick(card) {
    if (card.isLocked) {
      wx.showToast({
        title: '英雄未解锁',
        icon: 'none'
      })
    } else {
      wx.showToast({
        title: `选择了${card.name}`,
        icon: 'none'
      })
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    // 英雄页面暂时不需要动画更新
  }
  
  /**
   * 更新动画参数
   */
  updateRotation() {
    // 每帧增加1度，实现平滑旋转
    this.rotationAngle += 1
    if (this.rotationAngle >= 360) {
      this.rotationAngle = 0
    }

    // 更新若隐若现动画时间
    this.fadeTime += 3  // 控制若隐若现的速度（加快变化）
    if (this.fadeTime >= 360) {
      this.fadeTime = 0
    }

    // 更新序列帧动画
    this.updateSequenceAnimation()

    // 更新统一返回按钮组件动画
    this.backButtonComponent.update()
  }

  /**
   * 更新序列帧动画
   */
  updateSequenceAnimation() {
    const frameInterval = 8  // 每8帧切换一次序列帧 (约133ms)

    this.sequenceFrameTimer++
    if (this.sequenceFrameTimer >= frameInterval) {
      this.sequenceFrameTimer = 0
      this.sequenceFrameIndex++

      // 循环播放：10帧循环 (0-9)
      if (this.sequenceFrameIndex >= 10) {
        this.sequenceFrameIndex = 0
      }
    }
  }

  /**
   * 更新返回按钮序列帧动画
   */
  updateBackButtonAnimation() {
    const frameInterval = 6  // 每6帧切换一次 (约100ms)，稍快一些

    this.backButtonFrameTimer++
    if (this.backButtonFrameTimer >= frameInterval) {
      this.backButtonFrameTimer = 0
      this.backButtonFrameIndex++

      // 循环播放：8帧循环 (0-7)
      if (this.backButtonFrameIndex >= 8) {
        this.backButtonFrameIndex = 0
      }
    }
  }

  /**
   * 渲染场景
   */
  render() {
    // 更新边框动画
    imageBorderRenderer.updateAnimation()

    // 更新旋转角度
    this.updateRotation()

    // 更新导航栏动画
    this.updateNavigationAnimation()

    // 应用像素比缩放
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 绘制背景
    this.drawBackground()

    // 应用水平滚动偏移绘制卡片（在底层）
    this.ctx.save()
    this.ctx.translate(-this.scrollX, 0)

    // 绘制英雄卡片
    this.drawHeroCards()

    this.ctx.restore()

    // 绘制固定UI（在最上层，不受滚动影响）
    this.drawLeftNavigation()
    this.backButtonComponent.draw(this.ctx)

    this.ctx.restore()
  }
  
  /**
   * 绘制背景 - 支持图片背景
   */
  drawBackground() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()

    if (this.heroSceneBgImage) {
      // 使用图片背景
      this.ctx.save()

      // 计算图片缩放比例，保持宽高比并填满屏幕
      const imgWidth = this.heroSceneBgImage.width
      const imgHeight = this.heroSceneBgImage.height
      const scaleX = windowWidth / imgWidth
      const scaleY = windowHeight / imgHeight
      const scale = Math.max(scaleX, scaleY) // 使用较大的缩放比例确保填满屏幕

      // 计算居中位置
      const scaledWidth = imgWidth * scale
      const scaledHeight = imgHeight * scale
      const offsetX = (windowWidth - scaledWidth) / 2
      const offsetY = (windowHeight - scaledHeight) / 2

      // 绘制背景图片
      this.ctx.drawImage(
        this.heroSceneBgImage,
        offsetX, offsetY,
        scaledWidth, scaledHeight
      )

      this.ctx.restore()
    } else {
      // 回退到渐变背景
      const gradient = this.ctx.createLinearGradient(0, 0, windowWidth, windowHeight)
      gradient.addColorStop(0, '#2c3e50')
      gradient.addColorStop(0.5, '#34495e')
      gradient.addColorStop(1, '#2c3e50')

      this.ctx.fillStyle = gradient
      this.ctx.fillRect(0, 0, windowWidth, windowHeight)
    }
  }
  
  /**
   * 绘制垂直胶囊式导航栏
   */
  drawLeftNavigation() {
    this.ctx.save()

    // 绘制导航按钮（圆形胶囊）
    this.leftNavButtons.forEach(button => {
      if (button.alpha > 0) {  // 只绘制可见的按钮
        this.drawCapsuleNavButton(button)
      }
    })

    this.ctx.restore()

    // 绘制切换按钮（胶囊形状）
    this.drawNavToggleButton()
  }

  /**
   * 绘制长方形切换按钮
   */
  drawNavToggleButton() {
    const { x, y, width, height, isPressed } = this.navToggleButton
    const radius = 8  // 小圆角

    this.ctx.save()

    // 绘制长方形背景
    this.ctx.fillStyle = isPressed ? 'rgba(0, 0, 0, 0.9)' : 'rgba(0, 0, 0, 0.75)'
    this.drawRoundedRect(x - width/2, y - height/2, width, height, radius)
    this.ctx.fill()

    // 绘制边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'
    this.ctx.lineWidth = 1.5
    this.drawRoundedRect(x - width/2, y - height/2, width, height, radius)
    this.ctx.stroke()

    // 绘制三条横线图标（更紧凑）
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.lineCap = 'round'

    const lineWidth = 16  // 更短的线条
    const lineSpacing = 3  // 更紧凑的间距
    const startX = x - lineWidth / 2
    const startY = y - lineSpacing

    // 三条横线
    for (let i = 0; i < 3; i++) {
      this.ctx.beginPath()
      this.ctx.moveTo(startX, startY + i * lineSpacing)
      this.ctx.lineTo(startX + lineWidth, startY + i * lineSpacing)
      this.ctx.stroke()
    }

    this.ctx.restore()
  }

  /**
   * 绘制紧凑长方形导航按钮
   */
  drawCapsuleNavButton(button) {
    const { x, y, width, height, text, icon, isPressed, isActive, alpha } = button
    const radius = 6  // 小圆角

    this.ctx.save()

    // 设置透明度
    this.ctx.globalAlpha = alpha

    // 绘制长方形背景
    this.ctx.fillStyle = isActive ?
      (isPressed ? '#2471A3' : '#3498DB') :
      (isPressed ? 'rgba(0, 0, 0, 0.85)' : 'rgba(0, 0, 0, 0.7)')

    this.drawRoundedRect(x - width/2, y - height/2, width, height, radius)
    this.ctx.fill()

    // 绘制边框
    this.ctx.strokeStyle = isActive ? 'rgba(255, 255, 255, 0.9)' : 'rgba(255, 255, 255, 0.7)'
    this.ctx.lineWidth = isActive ? 2 : 1.5
    this.drawRoundedRect(x - width/2, y - height/2, width, height, radius)
    this.ctx.stroke()

    // 绘制文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 14px Arial'  // 适合长方形的文字大小
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(text, x, y)

    this.ctx.restore()
  }

  /**
   * 绘制可收回导航按钮（保留原方法以防需要）
   */
  drawCollapsibleNavButton(button) {
    const { x, y, width, height, text, icon, isPressed, isActive } = button
    const rectX = x - width / 2
    const rectY = y - height / 2
    const radius = 8

    this.ctx.save()

    // 按钮背景
    let bgColor, borderColor
    if (isActive) {
      bgColor = isPressed ? '#2471A3' : '#3498DB'
      borderColor = '#1B4F72'
    } else {
      bgColor = isPressed ? '#5D6D7E' : 'rgba(255, 255, 255, 0.15)'
      borderColor = 'rgba(255, 255, 255, 0.3)'
    }

    this.ctx.fillStyle = bgColor
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.fill()

    // 按钮边框
    this.ctx.strokeStyle = borderColor
    this.ctx.lineWidth = 1.5
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.stroke()

    // 绘制内容
    this.ctx.fillStyle = '#ffffff'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    if (this.navAnimationProgress > 0.5) {
      // 展开状态：图标在左侧，文字在右侧（更紧凑）
      this.ctx.font = 'bold 16px Arial'  // 更小的图标
      this.ctx.fillText(icon, x - width / 3.5, y)

      this.ctx.font = 'bold 12px Arial'  // 更小的文字
      this.ctx.fillText(text, x + width / 8, y)
    } else {
      // 收起状态：只显示图标居中
      this.ctx.font = 'bold 18px Arial'  // 更小的图标
      this.ctx.fillText(icon, x, y)
    }

    this.ctx.restore()
  }

  /**
   * 绘制导航按钮 - 重新设计不透明背景
   */
  drawNavButton(button) {
    const { x, y, width, height, text, icon, isPressed, isActive } = button

    const rectX = x - width / 2
    const rectY = y - height / 2
    const radius = 6

    this.ctx.save()

    // 按钮背景 - 不透明设计
    let bgColor, borderColor
    if (isActive) {
      // 激活状态：蓝色系
      bgColor = isPressed ? '#2471A3' : '#3498DB'
      borderColor = '#1B4F72'
    } else {
      // 非激活状态：灰色系
      bgColor = isPressed ? '#5D6D7E' : '#85929E'
      borderColor = '#34495E'
    }

    // 绘制背景
    this.ctx.fillStyle = bgColor
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.fill()

    // 绘制边框
    this.ctx.strokeStyle = borderColor
    this.ctx.lineWidth = 1.5
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.stroke()

    // 添加内阴影效果
    if (isPressed) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'
      this.drawRoundedRect(rectX + 1, rectY + 1, width - 2, height - 2, radius - 1)
      this.ctx.fill()
    }

    // 绘制图标
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(icon, x, y - 6)

    // 绘制文字
    this.ctx.fillStyle = isActive ? '#FFFFFF' : '#F8F9FA'
    this.ctx.font = 'bold 9px Arial'
    this.ctx.fillText(text, x, y + 8)

    this.ctx.restore()
  }

  /**
   * 绘制英雄卡片
   */
  drawHeroCards() {
    const filteredHeroes = this.getFilteredHeroes()

    // 重新计算筛选后英雄的位置
    const leftNavWidth = 100
    const sideMargin = 20
    const cardSpacing = 10
    const centerY = this.heroCards[0].y // 使用原始的Y位置

    filteredHeroes.forEach((card, index) => {
      // 创建临时卡片对象，重新计算X位置
      const displayCard = {
        ...card,
        x: leftNavWidth + sideMargin + card.width / 2 + index * (card.width + cardSpacing),
        y: centerY
      }

      this.drawHeroCard(displayCard)
    })
  }

  /**
   * 绘制单个英雄卡片 - 按指定顺序绘制
   */
  drawHeroCard(card) {
    const { x, y, width, height, name, level, rarity, heroClass, isLocked, isPressed } = card

    const rectX = x - width / 2
    const rectY = y - height / 2

    // 按指定顺序绘制：品质背景 → 序列帧 → 若隐若现 → 反向旋转 → 正向旋转 → 边框 → 背景 → 星星 → 名字 → 等级（文字在最上层）

    // 1. 先绘制品质纯色背景（最底层）
    this.drawQualityGradient(card, rectX, rectY, width, height, rarity)

    // 2. 绘制品质序列帧动画
    this.drawSequenceFrame(card, rectX, rectY, width, height, rarity)

    // 3. 绘制若隐若现特效
    this.drawFadeEffect(card, rectX, rectY, width, height)

    // 4. 绘制反向旋转特效
    this.drawRotationEffectReverse(card, rectX, rectY, width, height)

    // 5. 绘制正向旋转特效
    this.drawRotationEffect(card, rectX, rectY, width, height)

    // 6. 绘制边框
    this.drawChineseStyleBorder(rectX, rectY, width, height, rarity, isPressed)

    // 7. 绘制背景（在边框之上）
    this.drawCardBackground(card, rectX, rectY, width, height, rarity, isLocked, isPressed)

    // 8. 绘制星星（在背景之上）
    this.drawStars(card, rectX, rectY, width, height, isLocked)

    // 9. 绘制名字（在背景之上）
    this.drawHeroName(card, rectX, rectY, width, height, isLocked)

    // 10. 最后绘制等级（最上层）
    this.drawHeroLevel(card, rectX, rectY, width, height, isLocked)
  }

  /**
   * 绘制品质纯色背景（第1层 - 最底层）
   */
  drawQualityGradient(card, rectX, rectY, width, height, rarity) {
    const borderWidth = BORDER_WIDTH
    const innerX = rectX + borderWidth - CONTENT_OFFSET_X
    const innerY = rectY + borderWidth + CONTENT_OFFSET_Y_TOP
    const innerWidth = width - borderWidth * 2
    const innerHeight = height - borderWidth * 2 - CONTENT_OFFSET_Y_TOP - CONTENT_OFFSET_Y_BOTTOM

    const rarityColor = this.getRarityColor(rarity)

    this.ctx.save()

    // 设置纯色背景（不透明）
    this.ctx.fillStyle = rarityColor

    // 创建圆角矩形路径
    this.drawRoundedRect(innerX, innerY, innerWidth, innerHeight, 6)
    this.ctx.fill()

    this.ctx.restore()
  }

  /**
   * 绘制品质序列帧动画（第2层）
   */
  drawSequenceFrame(card, rectX, rectY, width, height, rarity) {
    // 检查序列帧是否已加载
    if (!this.sequenceFrames || !this.sequenceFrames[rarity]) {
      return  // 序列帧未加载，跳过绘制
    }

    const frames = this.sequenceFrames[rarity]
    if (!frames || frames.length === 0) return

    // 确保索引在有效范围内
    const safeIndex = this.sequenceFrameIndex % Math.max(1, frames.length)
    const currentFrame = frames[safeIndex]
    if (!currentFrame) return

    // 计算内容区域位置和尺寸
    const borderWidth = BORDER_WIDTH
    const innerX = rectX + borderWidth - CONTENT_OFFSET_X
    const innerY = rectY + borderWidth + CONTENT_OFFSET_Y_TOP
    const innerWidth = width - borderWidth * 2
    const innerHeight = height - borderWidth * 2 - CONTENT_OFFSET_Y_TOP - CONTENT_OFFSET_Y_BOTTOM

    this.ctx.save()

    // 设置适中的透明度，让序列帧不会过于抢眼
    this.ctx.globalAlpha = 0.6

    // 序列帧图片原始尺寸：169x112像素，比例约1.51:1
    // 计算保持比例的显示尺寸
    const frameAspectRatio = 169 / 112  // 约1.51
    const containerAspectRatio = innerWidth / innerHeight

    let displayWidth, displayHeight, displayX, displayY

    if (frameAspectRatio > containerAspectRatio) {
      // 序列帧更宽，以宽度为准
      displayWidth = innerWidth
      displayHeight = innerWidth / frameAspectRatio
      displayX = innerX
      // 再往下调整：稍微超出底部边界
      displayY = innerY + (innerHeight - displayHeight) * 1.1  // 1.1表示超出底部显示
    } else {
      // 序列帧更高，以高度为准
      displayHeight = innerHeight
      displayWidth = innerHeight * frameAspectRatio
      displayX = innerX + (innerWidth - displayWidth) / 2
      // 再往下调整：超出内容区底部
      displayY = innerY + 150  // 向下偏移150像素，可能超出边界
    }

    // 绘制序列帧图片，保持原始比例
    this.ctx.drawImage(
      currentFrame,
      displayX, displayY,
      displayWidth, displayHeight
    )

    this.ctx.restore()
  }

  /**
   * 绘制若隐若现特效（第3层）
   */
  drawFadeEffect(card, rectX, rectY, width, height) {
    if (!this.fadeEffectImage) return

    const centerX = rectX + width / 2
    const centerY = rectY + height / 2
    const effectWidth = 162   // 若隐若现特效宽度（9:16比例，稍大）
    const effectHeight = 288  // 若隐若现特效高度（9:16比例，稍大）

    this.ctx.save()

    // 移动到卡片中心
    this.ctx.translate(centerX, centerY)

    // 计算若隐若现的透明度（使用正弦波，范围0.1-0.4，更明显）
    const fadeAlpha = 0.1 + (Math.sin(this.fadeTime * Math.PI / 180) + 1) / 2 * 0.3
    this.ctx.globalAlpha = fadeAlpha

    // 绘制若隐若现特效图片（垂直长方形，不旋转，只是透明度变化）
    this.ctx.drawImage(
      this.fadeEffectImage,
      -effectWidth / 2,
      -effectHeight / 2,
      effectWidth,
      effectHeight
    )

    this.ctx.restore()
  }

  /**
   * 绘制反向旋转特效（第2层）
   */
  drawRotationEffectReverse(card, rectX, rectY, width, height) {
    if (!this.rotationEffectReverseImage) return

    const centerX = rectX + width / 2
    const centerY = rectY + height / 2
    const effectSize = 140 // 反向旋转特效稍大一些

    this.ctx.save()

    // 移动到卡片中心
    this.ctx.translate(centerX, centerY)

    // 反向旋转（逆时针）
    this.ctx.rotate(-this.rotationAngle * Math.PI / 180)

    // 设置更低的透明度，作为背景层
    this.ctx.globalAlpha = 0.2

    // 绘制反向旋转特效图片
    this.ctx.drawImage(
      this.rotationEffectReverseImage,
      -effectSize / 2,
      -effectSize / 2,
      effectSize,
      effectSize
    )

    this.ctx.restore()
  }

  /**
   * 绘制正向旋转特效（第2层）
   */
  drawRotationEffect(card, rectX, rectY, width, height) {
    if (!this.rotationEffectImage) return

    const centerX = rectX + width / 2
    const centerY = rectY + height / 2
    const effectSize = 120 // 特效图片大小（增大到120px）

    this.ctx.save()

    // 移动到卡片中心
    this.ctx.translate(centerX, centerY)

    // 旋转
    this.ctx.rotate(this.rotationAngle * Math.PI / 180)

    // 设置透明度，让特效不太抢眼
    this.ctx.globalAlpha = 0.3

    // 绘制旋转特效图片
    this.ctx.drawImage(
      this.rotationEffectImage,
      -effectSize / 2,
      -effectSize / 2,
      effectSize,
      effectSize
    )

    this.ctx.restore()
  }

  /**
   * 绘制星星（第4层）
   */
  drawStars(card, rectX, rectY, width, height, isLocked) {
    if (isLocked) return

    const { starLevel } = card
    const borderWidth = BORDER_WIDTH
    const innerX = rectX + borderWidth - CONTENT_OFFSET_X
    const innerY = rectY + borderWidth + CONTENT_OFFSET_Y_TOP
    const innerHeight = height - borderWidth * 2 - CONTENT_OFFSET_Y_TOP - CONTENT_OFFSET_Y_BOTTOM
    const contentCenterX = innerX + (width - borderWidth * 2) / 2

    // 星级显示 - 使用图片显示在内容区域底部
    starRenderer.drawStars(this.ctx, contentCenterX, innerY + innerHeight - 25, starLevel)
  }

  /**
   * 绘制英雄名字（第2层）
   */
  drawHeroName(card, rectX, rectY, width, height, isLocked) {
    const { name } = card
    const borderWidth = BORDER_WIDTH
    const innerX = rectX + borderWidth - CONTENT_OFFSET_X
    const innerY = rectY + borderWidth + CONTENT_OFFSET_Y_TOP
    const innerHeight = height - borderWidth * 2 - CONTENT_OFFSET_Y_TOP - CONTENT_OFFSET_Y_BOTTOM
    const contentCenterX = innerX + (width - borderWidth * 2) / 2

    // 英雄名称 - 显示在星星上面
    this.ctx.fillStyle = isLocked ? '#999999' : '#ffffff'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    // 显示在星星上面，距离星星位置上方20px（往上移动）
    this.ctx.fillText(name, contentCenterX, innerY + innerHeight - 25 - 20)
  }

  /**
   * 绘制等级（第3层）
   */
  drawHeroLevel(card, rectX, rectY, width, height, isLocked) {
    if (isLocked) return

    const { level } = card
    const borderWidth = BORDER_WIDTH
    const innerX = rectX + borderWidth - CONTENT_OFFSET_X
    const innerY = rectY + borderWidth + CONTENT_OFFSET_Y_TOP

    // 等级显示 - 显示在内容区域左上角
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '16px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'top'
    // 显示在内容区域左上角，再往右移动到距离左边缘30px
    this.ctx.fillText(`Lv.${level}`, innerX + 30, innerY + 8)

    // 恢复默认对齐方式
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
  }

  /**
   * 绘制卡片背景（第4层）
   */
  drawCardBackground(card, rectX, rectY, width, height, rarity, isLocked, isPressed) {
    const { id, heroClass } = card
    const rarityColor = this.getRarityColor(rarity)
    const borderWidth = BORDER_WIDTH
    const innerX = rectX + borderWidth - CONTENT_OFFSET_X
    const innerY = rectY + borderWidth + CONTENT_OFFSET_Y_TOP
    const innerWidth = width - borderWidth * 2
    const innerHeight = height - borderWidth * 2 - CONTENT_OFFSET_Y_TOP - CONTENT_OFFSET_Y_BOTTOM

    // 内部背景 - 所有英雄都尝试使用图片背景，失败时使用渐变背景
    const heroBgImage = this.heroBgImages && this.heroBgImages[id]
    if (heroBgImage) {
      // 使用背景图片 - 扩大背景图片区域
      this.ctx.save()

      // 扩大背景图片的显示区域，向外扩展20px
      const bgExpand = 20
      const bgX = innerX - bgExpand
      const bgY = innerY - bgExpand
      const bgWidth = innerWidth + bgExpand * 2
      const bgHeight = innerHeight + bgExpand * 2

      // 创建圆角裁剪区域（仍然使用原来的内容区域）
      this.drawRoundedRect(innerX, innerY, innerWidth, innerHeight, 6)
      this.ctx.clip()

      // 绘制更大的背景图片
      this.ctx.drawImage(heroBgImage, bgX, bgY, bgWidth, bgHeight)

      // 如果是按压状态，添加半透明遮罩
      if (isPressed) {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)'
        this.ctx.fillRect(innerX, innerY, innerWidth, innerHeight)
      }

      this.ctx.restore()
    } else {
      // 其他英雄使用渐变背景
      const gradient = this.ctx.createLinearGradient(innerX, innerY, innerX, innerY + innerHeight)
      if (isLocked) {
        gradient.addColorStop(0, 'rgba(100, 100, 100, 0.9)')
        gradient.addColorStop(1, 'rgba(80, 80, 80, 0.9)')
      } else if (isPressed) {
        gradient.addColorStop(0, this.hexToRgba(this.adjustColor(rarityColor, -20), 0.9))
        gradient.addColorStop(1, this.hexToRgba(this.adjustColor(rarityColor, -40), 0.9))
      } else {
        gradient.addColorStop(0, this.hexToRgba(rarityColor, 0.9))
        gradient.addColorStop(1, this.hexToRgba(this.adjustColor(rarityColor, -30), 0.8))
      }

      this.ctx.fillStyle = gradient
      this.drawRoundedRect(innerX, innerY, innerWidth, innerHeight, 6)
      this.ctx.fill()
    }

    // 职业图标显示 - 基于内容区域定位
    if (!isLocked) {
      const contentCenterX = innerX + innerWidth / 2
      const contentCenterY = innerY + innerHeight / 2
      const classIcon = this.getHeroClassIcon(heroClass)
      this.ctx.fillStyle = '#ffffff'
      this.ctx.font = '14px Arial'
      this.ctx.fillText(classIcon, contentCenterX - 30, contentCenterY + 35)
    }

    // 锁定图标 - 显示在内容区域中心
    if (isLocked) {
      const contentCenterX = innerX + innerWidth / 2
      const contentCenterY = innerY + innerHeight / 2
      this.ctx.fillStyle = '#e74c3c'
      this.ctx.font = '20px Arial'
      this.ctx.fillText('🔒', contentCenterX, contentCenterY)
    }
  }



  /**
   * 获取英雄职业图标
   */
  getHeroClassIcon(heroClass) {
    const classIcons = {
      warrior: '⚔️',
      mage: '🔮',
      archer: '🏹',
      support: '🛡️'
    }

    return classIcons[heroClass] || '⚔️'
  }

  /**
   * 绘制卡片边框（优先使用图片，回退到代码绘制）
   */
  drawChineseStyleBorder(x, y, width, height, rarity, isPressed) {
    // 尝试使用图片边框
    if (imageBorderRenderer.isReady()) {
      imageBorderRenderer.drawImageBorder(this.ctx, x, y, width, height, rarity, isPressed)
      return
    }

    // 回退到代码绘制
    this.drawCodeBorder(x, y, width, height, rarity, isPressed)
  }

  /**
   * 代码绘制边框（作为回退方案）
   */
  drawCodeBorder(x, y, width, height, rarity, isPressed) {
    const borderWidth = 8
    const cornerSize = 20

    // 获取稀有度对应的边框颜色
    const borderColors = this.getChineseBorderColors(rarity)

    // 绘制外层阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'
    this.drawRoundedRect(x + 3, y + 3, width, height, 8)
    this.ctx.fill()

    // 绘制主边框背景
    const mainColor = isPressed ? borderColors.pressed : borderColors.main
    this.ctx.fillStyle = mainColor
    this.drawRoundedRect(x, y, width, height, 8)
    this.ctx.fill()

    // 绘制内边框
    this.ctx.fillStyle = borderColors.inner
    this.drawRoundedRect(x + 2, y + 2, width - 4, height - 4, 6)
    this.ctx.fill()

    // 绘制中国风装饰元素
    this.drawChineseDecorations(x, y, width, height, borderColors, rarity)

    // 绘制四角装饰
    this.drawCornerDecorations(x, y, width, height, borderColors)
  }

  /**
   * 获取中国风边框颜色
   */
  getChineseBorderColors(rarity) {
    const colorSchemes = {
      common: {
        main: '#8B7355',      // 古铜色
        inner: '#A0956B',     // 浅古铜色
        pressed: '#6B5A42',   // 深古铜色
        accent: '#D4AF37',    // 金色装饰
        text: '#2F1B14'       // 深棕色文字
      },
      rare: {
        main: '#4A90E2',      // 青花蓝
        inner: '#6BA3E8',     // 浅青花蓝
        pressed: '#3A7BC8',   // 深青花蓝
        accent: '#87CEEB',    // 天蓝色装饰
        text: '#1E3A8A'       // 深蓝色文字
      },
      epic: {
        main: '#8B5CF6',      // 紫禁城紫
        inner: '#A78BFA',     // 浅紫色
        pressed: '#7C3AED',   // 深紫色
        accent: '#DDD6FE',    // 淡紫色装饰
        text: '#4C1D95'       // 深紫色文字
      },
      legendary: {
        main: '#F59E0B',      // 皇家金
        inner: '#FCD34D',     // 浅金色
        pressed: '#D97706',   // 深金色
        accent: '#FEF3C7',    // 淡金色装饰
        text: '#92400E'       // 深金棕色文字
      }
    }

    return colorSchemes[rarity] || colorSchemes.common
  }

  /**
   * 绘制中国风装饰元素
   */
  drawChineseDecorations(x, y, width, height, colors, rarity) {
    // 顶部装饰条纹
    this.ctx.fillStyle = colors.accent
    this.drawRoundedRect(x + 6, y + 6, width - 12, 4, 2)
    this.ctx.fill()

    // 底部装饰条纹
    this.drawRoundedRect(x + 6, y + height - 10, width - 12, 4, 2)
    this.ctx.fill()

    // 根据稀有度绘制特殊装饰
    if (rarity === 'legendary') {
      // 传说级：绘制龙纹样式的装饰线
      this.drawDragonPattern(x, y, width, height, colors)
    } else if (rarity === 'epic') {
      // 史诗级：绘制云纹装饰
      this.drawCloudPattern(x, y, width, height, colors)
    }
  }

  /**
   * 绘制四角装饰
   */
  drawCornerDecorations(x, y, width, height, colors) {
    const cornerSize = 12
    const cornerThickness = 3

    this.ctx.strokeStyle = colors.accent
    this.ctx.lineWidth = cornerThickness
    this.ctx.lineCap = 'round'

    // 左上角
    this.ctx.beginPath()
    this.ctx.moveTo(x + 4, y + cornerSize + 4)
    this.ctx.lineTo(x + 4, y + 4)
    this.ctx.lineTo(x + cornerSize + 4, y + 4)
    this.ctx.stroke()

    // 右上角
    this.ctx.beginPath()
    this.ctx.moveTo(x + width - cornerSize - 4, y + 4)
    this.ctx.lineTo(x + width - 4, y + 4)
    this.ctx.lineTo(x + width - 4, y + cornerSize + 4)
    this.ctx.stroke()

    // 左下角
    this.ctx.beginPath()
    this.ctx.moveTo(x + 4, y + height - cornerSize - 4)
    this.ctx.lineTo(x + 4, y + height - 4)
    this.ctx.lineTo(x + cornerSize + 4, y + height - 4)
    this.ctx.stroke()

    // 右下角
    this.ctx.beginPath()
    this.ctx.moveTo(x + width - cornerSize - 4, y + height - 4)
    this.ctx.lineTo(x + width - 4, y + height - 4)
    this.ctx.lineTo(x + width - 4, y + height - cornerSize - 4)
    this.ctx.stroke()
  }

  /**
   * 绘制龙纹装饰（传说级）
   */
  drawDragonPattern(x, y, width, height, colors) {
    this.ctx.strokeStyle = this.hexToRgba(colors.accent, 0.5) // 50% 透明度
    this.ctx.lineWidth = 2

    // 绘制简化的龙纹曲线
    const centerX = x + width / 2
    const centerY = y + height / 2

    this.ctx.beginPath()
    this.ctx.moveTo(x + 15, centerY - 20)
    this.ctx.quadraticCurveTo(centerX, centerY - 40, x + width - 15, centerY - 20)
    this.ctx.quadraticCurveTo(centerX, centerY, x + 15, centerY + 20)
    this.ctx.quadraticCurveTo(centerX, centerY + 40, x + width - 15, centerY + 20)
    this.ctx.stroke()
  }

  /**
   * 绘制云纹装饰（史诗级）
   */
  drawCloudPattern(x, y, width, height, colors) {
    this.ctx.fillStyle = this.hexToRgba(colors.accent, 0.4) // 40% 透明度

    // 绘制简化的云纹
    const cloudY = y + height - 25
    for (let i = 0; i < 3; i++) {
      const cloudX = x + 20 + i * 30
      this.ctx.beginPath()
      this.ctx.arc(cloudX, cloudY, 8, 0, Math.PI * 2)
      this.ctx.fill()
    }
  }

  /**
   * 获取筛选后的英雄列表
   */
  getFilteredHeroes() {
    if (this.currentFilter === 'all') {
      return this.heroCards
    }

    return this.heroCards.filter(hero => hero.heroClass === this.currentFilter)
  }

  /**
   * 更新滚动范围
   */
  updateScrollRange() {
    const windowWidth = this.adapter.getWindowWidth()
    const filteredHeroes = this.getFilteredHeroes()
    const leftNavWidth = 100
    const sideMargin = 20
    const cardSpacing = 10

    if (filteredHeroes.length === 0) {
      this.maxScrollX = 0
      return
    }

    const cardWidth = filteredHeroes[0].width
    const totalWidth = filteredHeroes.length * (cardWidth + cardSpacing) - cardSpacing + leftNavWidth + sideMargin * 2
    this.maxScrollX = Math.max(0, totalWidth - windowWidth)
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath()
    this.ctx.moveTo(x + radius, y)
    this.ctx.lineTo(x + width - radius, y)
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    this.ctx.lineTo(x + width, y + height - radius)
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    this.ctx.lineTo(x + radius, y + height)
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    this.ctx.lineTo(x, y + radius)
    this.ctx.quadraticCurveTo(x, y, x + radius, y)
    this.ctx.closePath()
  }

  /**
   * 调整颜色亮度
   */
  adjustColor(color, amount) {
    const hex = color.replace('#', '')
    const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount))
    const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount))
    const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount))
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }

  /**
   * 将十六进制颜色转换为RGBA格式
   */
  hexToRgba(hex, alpha) {
    const cleanHex = hex.replace('#', '')
    const r = parseInt(cleanHex.substr(0, 2), 16)
    const g = parseInt(cleanHex.substr(2, 2), 16)
    const b = parseInt(cleanHex.substr(4, 2), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha})`
  }



  /**
   * 绘制返回按钮 - 精致的圆形设计
   */
  drawBackButton(button) {
    const { x, y, width, height, isPressed } = button

    this.ctx.save()

    // 检查序列帧是否已加载
    if (this.backButtonFrames && this.backButtonFrames.length > 0) {
      // 使用序列帧绘制
      const safeIndex = this.backButtonFrameIndex % Math.max(1, this.backButtonFrames.length)
      const currentFrame = this.backButtonFrames[safeIndex]

      if (currentFrame) {
        // 绘制序列帧图片（长方形）
        this.ctx.drawImage(
          currentFrame,
          x - width / 2, y - height / 2,  // 左上角位置
          width, height                   // 尺寸
        )

        // 如果按压，添加半透明遮罩（长方形）
        if (isPressed) {
          const radius = 8  // 长方形圆角
          this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
          this.drawRoundedRect(x - width / 2, y - height / 2, width, height, radius)
          this.ctx.fill()
        }
      } else {
        // 序列帧加载失败，使用备用绘制
        this.drawBackButtonFallback(button)
      }
    } else {
      // 序列帧未加载，使用备用绘制
      this.drawBackButtonFallback(button)
    }

    this.ctx.restore()
  }

  /**
   * 绘制返回按钮备用方案（序列帧加载失败时使用）
   */
  drawBackButtonFallback(button) {
    const { x, y, width, height, isPressed } = button
    const radius = 8  // 长方形圆角

    // 绘制长方形背景
    this.ctx.fillStyle = isPressed ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.6)'
    this.drawRoundedRect(x - width / 2, y - height / 2, width, height, radius)
    this.ctx.fill()

    // 绘制长方形边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.lineWidth = 1.5
    this.drawRoundedRect(x - width / 2, y - height / 2, width, height, radius)
    this.ctx.stroke()

    // 绘制箭头文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('←', x, y)
  }

  /**
   * 绘制按钮
   */
  drawButton(button) {
    const { x, y, width, height, text, isPressed } = button

    // 按钮背景
    this.ctx.fillStyle = isPressed ? '#c0392b' : '#e74c3c'
    this.ctx.fillRect(x - width / 2, y - height / 2, width, height)

    // 按钮边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x - width / 2, y - height / 2, width, height)

    // 按钮文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(text, x, y)
  }

  /**
   * 销毁场景
   */
  destroy() {
    // 清理触摸事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }
    if (this.touchMoveHandler) {
      wx.offTouchMove(this.touchMoveHandler)
    }

    // 清理其他资源
    this.touchStartHandler = null
    this.touchEndHandler = null
    this.touchMoveHandler = null
  }
}

// 导出模块
module.exports = HeroScene
