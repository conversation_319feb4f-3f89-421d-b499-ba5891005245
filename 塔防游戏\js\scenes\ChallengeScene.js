const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')

/**
 * 挑战页面场景
 */
class ChallengeScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 页面状态
    this.currentTab = 'adventure'  // 当前选中的标签页

    // 创建统一的返回按钮组件
    this.backButtonComponent = new BackButton(require('../utils/ImageManager.js'))

    // 设置UI元素
    this.setupUI()
    
    // 动画相关
    this.animationTime = 0
    this.particles = []
    this.initParticles()
    
    // 绑定触摸事件
    this.bindEvents()
  }
  
  /**
   * 设置UI元素
   */
  setupUI() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()
    const centerX = windowWidth / 2
    const centerY = windowHeight / 2
    
    // 返回按钮现在使用统一组件 this.backButtonComponent
    
    // 页面标题
    this.title = {
      text: '挑战',
      x: centerX,
      y: 80,
      fontSize: 32
    }
    
    // 三个大副本入口 - 水平排列，增加边距
    const cardWidth = 180  // 稍微减小卡片宽度
    const cardHeight = 280
    const cardSpacing = 25  // 减小卡片间距
    const sideMargin = 30   // 左右边距
    const availableWidth = windowWidth - sideMargin * 2  // 可用宽度
    const totalWidth = cardWidth * 3 + cardSpacing * 2

    // 如果总宽度超过可用宽度，进一步调整
    let finalCardWidth = cardWidth
    let finalSpacing = cardSpacing
    if (totalWidth > availableWidth) {
      finalCardWidth = Math.floor((availableWidth - cardSpacing * 2) / 3)
      finalSpacing = Math.floor((availableWidth - finalCardWidth * 3) / 2)
    }

    const finalTotalWidth = finalCardWidth * 3 + finalSpacing * 2
    const startX = centerX - finalTotalWidth / 2 + finalCardWidth / 2
    
    this.dungeonCards = {
      adventure: {
        title: '冒险关卡',
        subtitle: '主线剧情',
        icon: '🗺️',
        description: '探索未知的世界\n解锁新的挑战',
        progress: '进度: 12/50',
        x: startX,
        y: centerY - 20,
        width: finalCardWidth,
        height: cardHeight,
        isPressed: false,
        color: '#3498db'
      },
      challenge: {
        title: '挑战副本',
        subtitle: '极限挑战',
        icon: '⚔️',
        description: '测试你的策略\n获得丰厚奖励',
        progress: '今日: 3/5',
        x: startX + finalCardWidth + finalSpacing,
        y: centerY - 20,
        width: finalCardWidth,
        height: cardHeight,
        isPressed: false,
        color: '#e74c3c'
      },
      event: {
        title: '活动副本',
        subtitle: '限时活动',
        icon: '🎉',
        description: '限时开放副本\n珍稀道具等你来',
        progress: '剩余: 2天',
        x: startX + (finalCardWidth + finalSpacing) * 2,
        y: centerY - 20,
        width: finalCardWidth,
        height: cardHeight,
        isPressed: false,
        color: '#f39c12'
      }
    }
    

  }
  
  /**
   * 初始化粒子效果
   */
  initParticles() {
    for (let i = 0; i < 30; i++) {
      this.particles.push({
        x: Math.random() * this.adapter.getWindowWidth(),
        y: Math.random() * this.adapter.getWindowHeight(),
        vx: (Math.random() - 0.5) * 1,
        vy: (Math.random() - 0.5) * 1,
        size: Math.random() * 2 + 1,
        opacity: Math.random() * 0.3 + 0.1,
        color: ['#3498db', '#e74c3c', '#f39c12'][Math.floor(Math.random() * 3)]
      })
    }
  }
  
  /**
   * 绑定触摸事件
   */
  bindEvents() {
    // 保存事件处理器的引用，以便后续清理
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
    }
    
    // 检查副本卡片
    Object.keys(this.dungeonCards).forEach(key => {
      const card = this.dungeonCards[key]
      if (this.isPointInCard(pos, card)) {
        card.isPressed = true
      }
    })
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查返回按钮点击
    if (this.backButtonComponent.isPressed() && this.backButtonComponent.isPointInButton(pos)) {
      this.onBackClick()
    }
    this.backButtonComponent.setPressed(false)
    
    // 检查副本卡片点击
    Object.keys(this.dungeonCards).forEach(key => {
      const card = this.dungeonCards[key]
      if (card.isPressed && this.isPointInCard(pos, card)) {
        this.onDungeonClick(key)
      }
      card.isPressed = false
    })
  }
  
  /**
   * 检查点是否在按钮内
   */
  isPointInButton(pos, button) {
    return pos.x >= button.x - button.width / 2 &&
           pos.x <= button.x + button.width / 2 &&
           pos.y >= button.y - button.height / 2 &&
           pos.y <= button.y + button.height / 2
  }
  
  /**
   * 检查点是否在卡片内
   */
  isPointInCard(pos, card) {
    return pos.x >= card.x - card.width / 2 &&
           pos.x <= card.x + card.width / 2 &&
           pos.y >= card.y - card.height / 2 &&
           pos.y <= card.y + card.height / 2
  }
  
  /**
   * 返回按钮点击
   */
  onBackClick() {
    if (this.onSceneChange) {
      this.onSceneChange('game')
    }
  }
  
  /**
   * 副本卡片点击
   */
  onDungeonClick(dungeonType) {
    switch (dungeonType) {
      case 'adventure':
        wx.showToast({
          title: '冒险关卡开发中',
          icon: 'none'
        })
        break
      case 'challenge':
        wx.showToast({
          title: '挑战副本开发中',
          icon: 'none'
        })
        break
      case 'event':
        wx.showToast({
          title: '活动副本开发中',
          icon: 'none'
        })
        break
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    this.animationTime += deltaTime
    
    // 更新粒子
    this.particles.forEach(particle => {
      particle.x += particle.vx
      particle.y += particle.vy
      
      // 边界检查
      if (particle.x < 0 || particle.x > this.adapter.getWindowWidth()) {
        particle.vx = -particle.vx
      }
      if (particle.y < 0 || particle.y > this.adapter.getWindowHeight()) {
        particle.vy = -particle.vy
      }
    })
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 更新统一返回按钮组件动画
    this.backButtonComponent.update()

    // 应用像素比缩放
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())
    
    // 绘制背景
    this.drawBackground()
    
    // 绘制粒子效果
    this.drawParticles()
    
    // 绘制标题
    this.drawTitle()
    
    // 绘制副本卡片
    this.drawDungeonCards()
    
    // 绘制统一返回按钮组件
    this.backButtonComponent.draw(this.ctx)

    this.ctx.restore()
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
    gradient.addColorStop(0, '#2c3e50')
    gradient.addColorStop(0.5, '#34495e')
    gradient.addColorStop(1, '#2c3e50')
    
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
  }
  
  /**
   * 绘制粒子效果
   */
  drawParticles() {
    this.particles.forEach(particle => {
      this.ctx.save()
      this.ctx.globalAlpha = particle.opacity
      this.ctx.fillStyle = particle.color
      this.ctx.beginPath()
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.restore()
    })
  }
  
  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = `bold ${this.title.fontSize}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(this.title.text, this.title.x, this.title.y)
  }

  /**
   * 绘制副本卡片
   */
  drawDungeonCards() {
    Object.values(this.dungeonCards).forEach(card => {
      this.drawDungeonCard(card)
    })
  }

  /**
   * 绘制单个副本卡片
   */
  drawDungeonCard(card) {
    const { x, y, width, height, title, subtitle, icon, description, progress, color, isPressed } = card

    const rectX = x - width / 2
    const rectY = y - height / 2
    const radius = 15

    // 卡片阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
    this.drawRoundedRect(rectX + 3, rectY + 3, width, height, radius)
    this.ctx.fill()

    // 卡片背景渐变
    const gradient = this.ctx.createLinearGradient(rectX, rectY, rectX, rectY + height)
    if (isPressed) {
      gradient.addColorStop(0, this.adjustColor(color, -20))
      gradient.addColorStop(1, this.adjustColor(color, -40))
    } else {
      gradient.addColorStop(0, color)
      gradient.addColorStop(1, this.adjustColor(color, -20))
    }

    this.ctx.fillStyle = gradient
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.fill()

    // 卡片边框
    this.ctx.strokeStyle = isPressed ? '#ffffff' : 'rgba(255, 255, 255, 0.3)'
    this.ctx.lineWidth = 2
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.stroke()

    // 绘制图标
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '48px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(icon, x, y - 60)

    // 绘制标题
    this.ctx.font = 'bold 20px Arial'
    this.ctx.fillText(title, x, y - 10)

    // 绘制副标题
    this.ctx.font = '14px Arial'
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.fillText(subtitle, x, y + 15)

    // 绘制描述
    this.ctx.font = '12px Arial'
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
    const lines = description.split('\n')
    lines.forEach((line, index) => {
      this.ctx.fillText(line, x, y + 40 + index * 16)
    })

    // 绘制进度
    this.ctx.font = 'bold 12px Arial'
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillText(progress, x, y + 90)
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath()
    this.ctx.moveTo(x + radius, y)
    this.ctx.lineTo(x + width - radius, y)
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    this.ctx.lineTo(x + width, y + height - radius)
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    this.ctx.lineTo(x + radius, y + height)
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    this.ctx.lineTo(x, y + radius)
    this.ctx.quadraticCurveTo(x, y, x + radius, y)
    this.ctx.closePath()
  }

  /**
   * 调整颜色亮度
   */
  adjustColor(color, amount) {
    const hex = color.replace('#', '')
    const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount))
    const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount))
    const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount))
    return `rgb(${r}, ${g}, ${b})`
  }

  /**
   * 绘制返回按钮 - 长方形设计（与英雄页面统一样式）
   */
  drawBackButton(button) {
    const { x, y, width, height, text, isPressed } = button
    const radius = 8  // 长方形圆角

    this.ctx.save()

    // 绘制长方形背景
    this.ctx.fillStyle = isPressed ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.6)'
    this.drawRoundedRect(x - width / 2, y - height / 2, width, height, radius)
    this.ctx.fill()

    // 绘制长方形边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.lineWidth = 1.5
    this.drawRoundedRect(x - width / 2, y - height / 2, width, height, radius)
    this.ctx.stroke()

    // 绘制箭头文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(text, x, y)

    this.ctx.restore()
  }

  /**
   * 绘制按钮
   */
  drawButton(button) {
    const { x, y, width, height, text, isPressed } = button

    // 按钮背景
    this.ctx.fillStyle = isPressed ? '#c0392b' : '#e74c3c'
    this.ctx.fillRect(x - width / 2, y - height / 2, width, height)

    // 按钮边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(x - width / 2, y - height / 2, width, height)

    // 按钮文字
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(text, x, y)
  }



  /**
   * 销毁场景
   */
  destroy() {
    // 清理触摸事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    // 清理其他资源
    this.touchStartHandler = null
    this.touchEndHandler = null
  }
}

// 导出模块
module.exports = ChallengeScene
