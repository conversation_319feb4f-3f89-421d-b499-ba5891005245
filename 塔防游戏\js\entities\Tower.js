/**
 * 塔基类
 * 所有防御塔的基类
 */
const BaseEntity = require('./BaseEntity.js')
const TowerConfig = require('../config/TowerConfig.js')

class Tower extends BaseEntity {
  /**
   * 构造函数
   * @param {number} x - 塔的x坐标
   * @param {number} y - 塔的y坐标
   * @param {string} towerId - 塔的类型ID
   */
  constructor(x, y, towerId) {
    super(x, y)
    
    // 基本属性
    this.type = 'tower'
    this.towerId = towerId
    this.level = 1
    this.target = null
    
    // 加载塔配置
    this.loadConfig()
    
    // 攻击相关
    this.lastAttackTime = 0
    this.attackCooldown = 1000 / this.attackSpeed // 毫秒
    
    // 视觉相关
    this.rangeVisible = false
    this.upgradeAnimation = 0
    this.attackAnimation = 0
    
    // 状态
    this.isSelected = false
    this.isBuilding = true // 刚放置时处于建造状态
    this.buildProgress = 0 // 建造进度 0-1
  }
  
  /**
   * 加载塔配置
   */
  loadConfig() {
    const config = TowerConfig.getTowerLevelConfig(this.towerId, this.level)
    if (!config) {
      console.error(`找不到塔配置: ${this.towerId}, 等级: ${this.level}`)
      return
    }
    
    // 从配置中加载属性
    this.damage = config.damage
    this.range = config.range
    this.attackSpeed = config.attackSpeed
    this.cost = config.cost
    this.sellValue = config.sellValue
    
    // 加载特殊属性
    this.splashRadius = config.splashRadius || 0
    this.splashDamage = config.splashDamage || 0
    this.slowEffect = config.slowEffect || 0
    this.slowDuration = config.slowDuration || 0
    this.freezeChance = config.freezeChance || 0
    this.freezeDuration = config.freezeDuration || 0
    this.poisonDamage = config.poisonDamage || 0
    this.poisonDuration = config.poisonDuration || 0
    this.armorReduction = config.armorReduction || 0
    
    // 加载视觉配置
    const towerConfig = TowerConfig.getTowerConfig(this.towerId)
    if (towerConfig && towerConfig.visual) {
      this.color = towerConfig.visual.color || '#8B4513'
      this.size = towerConfig.visual.size || 32
      this.bulletType = towerConfig.visual.bulletType || 'arrow'
      this.attackEffect = towerConfig.visual.attackEffect || 'muzzle_flash'
    }
    
    // 加载目标选择策略
    if (towerConfig && towerConfig.targeting) {
      this.targetingPriority = towerConfig.targeting.priority || 'closest'
      this.canTargetAir = towerConfig.targeting.canTargetAir || false
      this.canTargetGround = towerConfig.targeting.canTargetGround || true
    }
    
    // 更新攻击冷却时间
    this.attackCooldown = 1000 / this.attackSpeed
    
    // 更新大小
    this.setSize(this.size, this.size)
  }
  
  /**
   * 升级塔
   * @returns {boolean} 是否升级成功
   */
  upgrade() {
    // 检查是否可以升级
    if (!TowerConfig.canUpgrade(this.towerId, this.level)) {
      return false
    }
    
    // 获取升级费用
    const upgradeCost = TowerConfig.getUpgradeCost(this.towerId, this.level)
    if (upgradeCost === null) {
      return false
    }
    
    // 升级等级
    this.level++
    
    // 重新加载配置
    this.loadConfig()
    
    // 触发升级动画
    this.upgradeAnimation = 1
    
    return true
  }
  
  /**
   * 获取出售价值
   */
  getSellValue() {
    return this.sellValue || 0
  }
  
  /**
   * 选择目标
   * @param {Array} enemies - 敌人列表
   */
  selectTarget(enemies) {
    if (!enemies || enemies.length === 0) {
      this.target = null
      return
    }
    
    // 筛选在范围内且可攻击的敌人
    const validTargets = enemies.filter(enemy => {
      // 检查敌人是否存活
      if (!enemy.isAlive || enemy.hp <= 0) return false
      
      // 检查距离
      const distance = this.distanceTo(enemy)
      if (distance > this.range) return false
      
      // 检查敌人类型
      if (enemy.type === 'air' && !this.canTargetAir) return false
      if (enemy.type === 'ground' && !this.canTargetGround) return false
      
      return true
    })
    
    if (validTargets.length === 0) {
      this.target = null
      return
    }
    
    // 根据目标选择策略选择敌人
    switch (this.targetingPriority) {
      case 'closest':
        this.target = this.findClosestEnemy(validTargets)
        break
      case 'strongest':
        this.target = this.findStrongestEnemy(validTargets)
        break
      case 'weakest':
        this.target = this.findWeakestEnemy(validTargets)
        break
      case 'first':
        this.target = this.findFirstEnemy(validTargets)
        break
      case 'last':
        this.target = this.findLastEnemy(validTargets)
        break
      default:
        this.target = this.findClosestEnemy(validTargets)
    }
  }
  
  /**
   * 寻找最近的敌人
   */
  findClosestEnemy(enemies) {
    let closest = null
    let minDistance = Infinity
    
    for (const enemy of enemies) {
      const distance = this.distanceTo(enemy)
      if (distance < minDistance) {
        minDistance = distance
        closest = enemy
      }
    }
    
    return closest
  }
  
  /**
   * 寻找最强的敌人（血量最高）
   */
  findStrongestEnemy(enemies) {
    return enemies.reduce((strongest, enemy) => {
      return (enemy.hp > strongest.hp) ? enemy : strongest
    }, enemies[0])
  }
  
  /**
   * 寻找最弱的敌人（血量最低）
   */
  findWeakestEnemy(enemies) {
    return enemies.reduce((weakest, enemy) => {
      return (enemy.hp < weakest.hp) ? enemy : weakest
    }, enemies[0])
  }
  
  /**
   * 寻找最靠前的敌人（路径进度最高）
   */
  findFirstEnemy(enemies) {
    return enemies.reduce((first, enemy) => {
      return (enemy.pathProgress > first.pathProgress) ? enemy : first
    }, enemies[0])
  }
  
  /**
   * 寻找最靠后的敌人（路径进度最低）
   */
  findLastEnemy(enemies) {
    return enemies.reduce((last, enemy) => {
      return (enemy.pathProgress < last.pathProgress) ? enemy : last
    }, enemies[0])
  }
  
  /**
   * 攻击目标
   * @returns {boolean} 是否成功攻击
   */
  attack() {
    if (!this.target || !this.target.isAlive) {
      return false
    }
    
    // 检查攻击冷却
    const currentTime = Date.now()
    if (currentTime - this.lastAttackTime < this.attackCooldown) {
      return false
    }
    
    // 更新攻击时间
    this.lastAttackTime = currentTime
    
    // 触发攻击动画
    this.attackAnimation = 1
    
    // 朝向目标
    this.lookAt(this.target)
    
    // 创建子弹或直接造成伤害
    if (this.onAttack) {
      this.onAttack(this, this.target)
    }
    
    return true
  }
  
  /**
   * 更新塔状态
   */
  update(deltaTime) {
    super.update(deltaTime)
    
    // 更新建造进度
    if (this.isBuilding) {
      this.buildProgress += deltaTime / 1000  // 假设建造需要1秒
      if (this.buildProgress >= 1) {
        this.isBuilding = false
        this.buildProgress = 1
      }
    }
    
    // 更新升级动画
    if (this.upgradeAnimation > 0) {
      this.upgradeAnimation -= deltaTime / 500  // 升级动画持续0.5秒
      if (this.upgradeAnimation < 0) {
        this.upgradeAnimation = 0
      }
    }
    
    // 更新攻击动画
    if (this.attackAnimation > 0) {
      this.attackAnimation -= deltaTime / 300  // 攻击动画持续0.3秒
      if (this.attackAnimation < 0) {
        this.attackAnimation = 0
      }
    }
    
    // 如果有目标，检查目标是否仍然有效
    if (this.target) {
      if (!this.target.isAlive || this.distanceTo(this.target) > this.range) {
        this.target = null
      }
    }
  }
  
  /**
   * 绘制塔
   */
  draw(ctx) {
    // 如果正在建造，绘制建造动画
    if (this.isBuilding) {
      this.drawBuildingAnimation(ctx)
      return
    }
    
    // 绘制塔基座
    this.drawBase(ctx)
    
    // 绘制塔身
    this.drawTower(ctx)
    
    // 绘制升级动画
    if (this.upgradeAnimation > 0) {
      this.drawUpgradeAnimation(ctx)
    }
    
    // 绘制攻击动画
    if (this.attackAnimation > 0) {
      this.drawAttackAnimation(ctx)
    }
    
    // 如果被选中，绘制选中效果
    if (this.isSelected) {
      this.drawSelectedEffect(ctx)
    }
    
    // 如果需要显示范围，绘制范围圈
    if (this.rangeVisible || this.isSelected) {
      this.drawRange(ctx)
    }
  }
  
  /**
   * 绘制塔基座
   */
  drawBase(ctx) {
    ctx.fillStyle = '#555555'
    ctx.beginPath()
    ctx.arc(0, 0, this.width / 2 + 4, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制塔身
   */
  drawTower(ctx) {
    ctx.fillStyle = this.color || '#8B4513'
    ctx.beginPath()
    ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2)
    ctx.fill()
    
    // 绘制等级指示器
    ctx.fillStyle = '#ffffff'
    ctx.font = '10px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(this.level.toString(), 0, 0)
  }
  
  /**
   * 绘制建造动画
   */
  drawBuildingAnimation(ctx) {
    // 绘制基座
    this.drawBase(ctx)
    
    // 绘制建造中的塔（半透明）
    ctx.globalAlpha = this.buildProgress
    this.drawTower(ctx)
    ctx.globalAlpha = 1
    
    // 绘制建造进度条
    const progressWidth = this.width * 1.2
    const progressHeight = 5
    
    ctx.fillStyle = '#333333'
    ctx.fillRect(-progressWidth / 2, this.height / 2 + 10, progressWidth, progressHeight)
    
    ctx.fillStyle = '#00ff00'
    ctx.fillRect(-progressWidth / 2, this.height / 2 + 10, progressWidth * this.buildProgress, progressHeight)
  }
  
  /**
   * 绘制升级动画
   */
  drawUpgradeAnimation(ctx) {
    const size = this.width * (1 + this.upgradeAnimation * 0.5)
    
    ctx.strokeStyle = '#ffff00'
    ctx.lineWidth = 2
    ctx.globalAlpha = 1 - this.upgradeAnimation
    
    ctx.beginPath()
    ctx.arc(0, 0, size / 2, 0, Math.PI * 2)
    ctx.stroke()
    
    ctx.globalAlpha = 1
  }
  
  /**
   * 绘制攻击动画
   */
  drawAttackAnimation(ctx) {
    ctx.fillStyle = '#ffffff'
    ctx.globalAlpha = 1 - this.attackAnimation
    
    // 绘制攻击闪光
    ctx.beginPath()
    ctx.arc(0, 0, this.width / 3, 0, Math.PI * 2)
    ctx.fill()
    
    ctx.globalAlpha = 1
  }
  
  /**
   * 绘制选中效果
   */
  drawSelectedEffect(ctx) {
    ctx.strokeStyle = '#ffffff'
    ctx.lineWidth = 2
    
    ctx.beginPath()
    ctx.arc(0, 0, this.width / 2 + 6, 0, Math.PI * 2)
    ctx.stroke()
  }
  
  /**
   * 绘制攻击范围
   */
  drawRange(ctx) {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)'
    ctx.lineWidth = 1
    
    ctx.beginPath()
    ctx.arc(0, 0, this.range, 0, Math.PI * 2)
    ctx.fill()
    ctx.stroke()
  }
  
  /**
   * 获取塔信息
   */
  getTowerInfo() {
    const towerConfig = TowerConfig.getTowerConfig(this.towerId)
    
    return {
      id: this.id,
      type: this.type,
      towerId: this.towerId,
      name: towerConfig ? towerConfig.name : '未知塔',
      level: this.level,
      damage: this.damage,
      range: this.range,
      attackSpeed: this.attackSpeed,
      sellValue: this.sellValue,
      position: { x: this.x, y: this.y },
      canUpgrade: TowerConfig.canUpgrade(this.towerId, this.level),
      upgradeCost: TowerConfig.getUpgradeCost(this.towerId, this.level)
    }
  }
}

// 导出模块
module.exports = Tower
