# 英雄背景图片资源

## 图片文件

请将英雄背景图片放入此目录：

- `hero_1_bg.png` - 英雄1背景图片
- `hero_2_bg.png` - 英雄2背景图片
- ... (其他英雄背景图片)

## 图片要求

- **尺寸**: 168x280px (比内容区域大40px，显示效果更饱满)
- **格式**: PNG/JPG (建议PNG以支持透明度)
- **设计**: 英雄主题背景，可以是角色立绘、场景等
- **风格**: 与游戏整体风格保持一致

## 使用说明

背景图片会显示在卡片内容区域内，作为卡片的背景。图片会比内容区域大40px，然后被裁剪到内容区域，这样可以避免边缘留白，显示效果更饱满。

## 显示层级顺序

卡片元素按以下顺序绘制（从底层到顶层）：
1. **边框** - 最底层（装饰框架）
2. **背景图片** - 第二层（显示在边框之上）
3. **星星** - 第三层（显示在背景之上）
4. **名字** - 第四层（显示在背景之上）
5. **等级** - 最顶层（显示在所有元素之上）

这样确保边框作为装饰底层，背景图片在边框之上，文字元素在最上层保证可读性。

## 命名规则

- 文件名格式：`hero_{id}_bg.png`
- 例如：英雄1 = `hero_1_bg.png`
- 例如：英雄15 = `hero_15_bg.png`
