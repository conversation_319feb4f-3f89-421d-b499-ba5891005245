const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')

/**
 * 冒险关卡地图场景
 * 支持左右拖动选择关卡
 */
class AdventureMapScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 创建返回按钮组件
    this.backButtonComponent = new BackButton(require('../utils/ImageManager.js'))
    
    // 关卡数据
    this.levels = this.initLevelData()
    
    // 拖动相关
    this.isDragging = false
    this.dragStartX = 0
    this.dragStartOffset = 0
    this.scrollOffset = 0  // 当前滚动偏移量
    this.targetOffset = 0  // 目标滚动偏移量
    this.scrollVelocity = 0  // 滚动速度
    this.friction = 0.95  // 摩擦力（增加以提高惯性滚动的流畅度）
    this.lastDragX = 0  // 上一次拖动位置
    this.lastDragTime = 0  // 上一次拖动时间
    this.dragVelocity = 0  // 拖动速度
    
    // 关卡卡片配置
    this.cardConfig = {
      width: 120,
      height: 160,
      spacing: 40,  // 卡片间距
      marginTop: 150  // 距离顶部的距离
    }
    
    // 计算总宽度和边界
    this.calculateBounds()
    
    // 动画相关
    this.animationTime = 0
    this.particles = []
    this.initParticles()
    
    // 绑定触摸事件
    this.bindEvents()
  }
  
  /**
   * 初始化关卡数据
   */
  initLevelData() {
    const levels = []
    
    // 创建20个关卡
    for (let i = 1; i <= 20; i++) {
      levels.push({
        id: i,
        name: `关卡 ${i}`,
        isUnlocked: i <= 5,  // 前5关解锁
        stars: i <= 3 ? 3 : (i <= 5 ? Math.floor(Math.random() * 3) + 1 : 0),
        difficulty: this.getDifficulty(i),
        background: this.getBackgroundType(i),
        enemies: Math.floor(i / 5) + 1,  // 敌人波数
        reward: {
          gold: i * 100,
          exp: i * 50
        }
      })
    }
    
    return levels
  }
  
  /**
   * 获取关卡难度
   */
  getDifficulty(level) {
    if (level <= 5) return 'easy'
    if (level <= 10) return 'normal'
    if (level <= 15) return 'hard'
    return 'expert'
  }
  
  /**
   * 获取背景类型
   */
  getBackgroundType(level) {
    const types = ['forest', 'desert', 'snow', 'volcano']
    return types[Math.floor((level - 1) / 5) % types.length]
  }
  
  /**
   * 计算边界
   */
  calculateBounds() {
    const windowWidth = this.adapter.getWindowWidth()
    const totalWidth = this.levels.length * (this.cardConfig.width + this.cardConfig.spacing) - this.cardConfig.spacing
    
    // 最小和最大滚动偏移量
    this.minOffset = Math.min(0, windowWidth - totalWidth - 100)  // 留100px右边距
    this.maxOffset = 100  // 左边距
  }
  
  /**
   * 初始化粒子效果
   */
  initParticles() {
    for (let i = 0; i < 40; i++) {
      this.particles.push({
        x: Math.random() * this.adapter.getWindowWidth(),
        y: Math.random() * this.adapter.getWindowHeight(),
        vx: (Math.random() - 0.5) * 1.5,
        vy: (Math.random() - 0.5) * 1.5,
        size: Math.random() * 2 + 1,
        opacity: Math.random() * 0.4 + 0.1,
        color: '#4a90e2'
      })
    }
  }
  
  /**
   * 绑定触摸事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchMove(this.touchMoveHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
      return
    }

    // 开始拖动
    this.isDragging = true
    this.dragStartX = pos.x
    this.dragStartOffset = this.scrollOffset
    this.scrollVelocity = 0
    this.lastDragX = pos.x
    this.lastDragTime = Date.now()
    this.dragVelocity = 0
  }
  
  /**
   * 触摸移动事件
   */
  onTouchMove(e) {
    if (!this.isDragging) return

    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    const currentTime = Date.now()

    // 计算拖动距离和速度
    const deltaX = pos.x - this.dragStartX
    const timeDelta = currentTime - this.lastDragTime

    if (timeDelta > 0) {
      this.dragVelocity = (pos.x - this.lastDragX) / timeDelta * 16  // 转换为每帧速度
    }

    this.targetOffset = this.dragStartOffset + deltaX

    // 添加边界弹性效果
    if (this.targetOffset > this.maxOffset) {
      this.targetOffset = this.maxOffset + (this.targetOffset - this.maxOffset) * 0.3
    } else if (this.targetOffset < this.minOffset) {
      this.targetOffset = this.minOffset + (this.targetOffset - this.minOffset) * 0.3
    }

    this.lastDragX = pos.x
    this.lastDragTime = currentTime
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查返回按钮点击
    if (this.backButtonComponent.isPressed() && this.backButtonComponent.isPointInButton(pos)) {
      this.onBackClick()
    }
    this.backButtonComponent.setPressed(false)
    
    if (this.isDragging) {
      this.isDragging = false

      // 如果拖动距离很小，检查是否点击了关卡
      const deltaX = Math.abs(pos.x - this.dragStartX)
      if (deltaX < 10) {
        this.checkLevelClick(pos)
      } else {
        // 使用计算出的拖动速度作为惯性滚动的初始速度
        this.scrollVelocity = this.dragVelocity * 0.8  // 稍微减少初始速度
      }

      // 确保目标位置在有效范围内
      this.targetOffset = Math.max(this.minOffset, Math.min(this.maxOffset, this.targetOffset))
    }
  }
  
  /**
   * 检查关卡点击
   */
  checkLevelClick(pos) {
    const windowWidth = this.adapter.getWindowWidth()
    const startX = this.scrollOffset + 100  // 左边距
    
    this.levels.forEach((level, index) => {
      const cardX = startX + index * (this.cardConfig.width + this.cardConfig.spacing)
      const cardY = this.cardConfig.marginTop + this.cardConfig.height / 2
      
      if (pos.x >= cardX - this.cardConfig.width / 2 &&
          pos.x <= cardX + this.cardConfig.width / 2 &&
          pos.y >= cardY - this.cardConfig.height / 2 &&
          pos.y <= cardY + this.cardConfig.height / 2) {
        this.onLevelClick(level)
      }
    })
  }
  
  /**
   * 关卡点击处理
   */
  onLevelClick(level) {
    if (!level.isUnlocked) {
      wx.showToast({
        title: '关卡未解锁',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: level.name,
      content: `难度: ${this.getDifficultyText(level.difficulty)}\n敌人波数: ${level.enemies}\n奖励金币: ${level.reward.gold}`,
      confirmText: '开始挑战',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 设置当前关卡ID并切换到战斗场景
          if (this.onSceneChange) {
            // 通过全局变量传递关卡ID（简化处理）
            if (typeof window !== 'undefined') {
              window.currentLevelId = level.id
            } else if (typeof global !== 'undefined') {
              global.currentLevelId = level.id
            }
            this.onSceneChange('battle')
          }
        }
      }
    })
  }
  
  /**
   * 返回按钮点击
   */
  onBackClick() {
    if (this.onSceneChange) {
      this.onSceneChange('challenge')
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    this.animationTime += deltaTime
    
    // 更新滚动动画
    if (!this.isDragging) {
      // 应用惯性滚动
      if (Math.abs(this.scrollVelocity) > 0.1) {
        this.targetOffset += this.scrollVelocity
        this.scrollVelocity *= this.friction
      }

      // 边界回弹效果
      if (this.targetOffset > this.maxOffset) {
        this.targetOffset = this.maxOffset
        this.scrollVelocity = 0
      } else if (this.targetOffset < this.minOffset) {
        this.targetOffset = this.minOffset
        this.scrollVelocity = 0
      }

      // 平滑滚动到目标位置
      const diff = this.targetOffset - this.scrollOffset
      this.scrollOffset += diff * 0.2  // 增加响应速度
    } else {
      // 拖动时直接更新
      this.scrollOffset = this.targetOffset
    }
    
    // 更新粒子
    this.particles.forEach(particle => {
      particle.x += particle.vx
      particle.y += particle.vy
      
      // 边界检查
      if (particle.x < 0 || particle.x > this.adapter.getWindowWidth()) {
        particle.vx = -particle.vx
      }
      if (particle.y < 0 || particle.y > this.adapter.getWindowHeight()) {
        particle.vy = -particle.vy
      }
    })
  }

  /**
   * 渲染场景
   */
  render() {
    // 更新返回按钮动画
    this.backButtonComponent.update()

    // 应用像素比缩放
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 绘制背景
    this.drawBackground()

    // 绘制粒子效果
    this.drawParticles()

    // 绘制标题
    this.drawTitle()

    // 绘制关卡卡片
    this.drawLevelCards()

    // 绘制进度指示器
    this.drawProgressIndicator()

    // 绘制返回按钮
    this.backButtonComponent.draw(this.ctx)

    this.ctx.restore()
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
    gradient.addColorStop(0, '#1e3c72')
    gradient.addColorStop(0.5, '#2a5298')
    gradient.addColorStop(1, '#1e3c72')

    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
  }

  /**
   * 绘制粒子效果
   */
  drawParticles() {
    this.particles.forEach(particle => {
      this.ctx.save()
      this.ctx.globalAlpha = particle.opacity
      this.ctx.fillStyle = particle.color
      this.ctx.beginPath()
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.restore()
    })
  }

  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 28px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('冒险关卡', this.adapter.getWindowWidth() / 2, 60)

    // 绘制副标题
    this.ctx.font = '16px Arial'
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
    this.ctx.fillText('左右滑动选择关卡', this.adapter.getWindowWidth() / 2, 90)
  }

  /**
   * 绘制关卡卡片
   */
  drawLevelCards() {
    const windowWidth = this.adapter.getWindowWidth()
    const startX = this.scrollOffset + 100  // 左边距

    this.levels.forEach((level, index) => {
      const cardX = startX + index * (this.cardConfig.width + this.cardConfig.spacing)
      const cardY = this.cardConfig.marginTop

      // 只绘制在屏幕范围内的卡片
      if (cardX + this.cardConfig.width / 2 >= -50 && cardX - this.cardConfig.width / 2 <= windowWidth + 50) {
        this.drawLevelCard(level, cardX, cardY)
      }
    })
  }

  /**
   * 绘制单个关卡卡片
   */
  drawLevelCard(level, x, y) {
    const { width, height } = this.cardConfig
    const radius = 12

    // 卡片阴影
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
    this.drawRoundedRect(x - width / 2 + 3, y + 3, width, height, radius)
    this.ctx.fill()

    // 卡片背景
    let bgColor = level.isUnlocked ? this.getDifficultyColor(level.difficulty) : '#666666'
    if (!level.isUnlocked) {
      bgColor = '#444444'
    }

    const gradient = this.ctx.createLinearGradient(x - width / 2, y, x - width / 2, y + height)
    gradient.addColorStop(0, bgColor)
    gradient.addColorStop(1, this.adjustColor(bgColor, -30))

    this.ctx.fillStyle = gradient
    this.drawRoundedRect(x - width / 2, y, width, height, radius)
    this.ctx.fill()

    // 卡片边框
    this.ctx.strokeStyle = level.isUnlocked ? 'rgba(255, 255, 255, 0.5)' : 'rgba(255, 255, 255, 0.2)'
    this.ctx.lineWidth = 2
    this.drawRoundedRect(x - width / 2, y, width, height, radius)
    this.ctx.stroke()

    // 绘制关卡编号
    this.ctx.fillStyle = level.isUnlocked ? '#ffffff' : '#888888'
    this.ctx.font = 'bold 24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(level.id.toString(), x, y + 30)

    // 绘制关卡名称
    this.ctx.font = '14px Arial'
    this.ctx.fillText(level.name, x, y + 55)

    // 绘制星级（如果已解锁且有星级）
    if (level.isUnlocked && level.stars > 0) {
      this.drawStars(x, y + 80, level.stars)
    }

    // 绘制锁定图标（如果未解锁）
    if (!level.isUnlocked) {
      this.ctx.fillStyle = '#888888'
      this.ctx.font = '32px Arial'
      this.ctx.fillText('🔒', x, y + 80)
    }

    // 绘制难度标识
    if (level.isUnlocked) {
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      this.ctx.font = '10px Arial'
      this.ctx.fillText(this.getDifficultyText(level.difficulty), x, y + height - 15)
    }
  }

  /**
   * 绘制星级
   */
  drawStars(x, y, stars) {
    const starSize = 8
    const starSpacing = 16
    const startX = x - (stars - 1) * starSpacing / 2

    for (let i = 0; i < 3; i++) {
      this.ctx.fillStyle = i < stars ? '#ffd700' : 'rgba(255, 255, 255, 0.3)'
      this.ctx.font = `${starSize}px Arial`
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('★', startX + i * starSpacing, y)
    }
  }

  /**
   * 绘制进度指示器
   */
  drawProgressIndicator() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()

    // 计算当前可见区域的进度
    const totalWidth = this.levels.length * (this.cardConfig.width + this.cardConfig.spacing)
    const visibleWidth = windowWidth - 200  // 减去左右边距
    const progress = Math.abs(this.scrollOffset - this.maxOffset) / Math.abs(this.minOffset - this.maxOffset)

    // 绘制进度条背景
    const barWidth = 200
    const barHeight = 4
    const barX = (windowWidth - barWidth) / 2
    const barY = windowHeight - 40

    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
    this.ctx.fillRect(barX, barY, barWidth, barHeight)

    // 绘制进度条
    this.ctx.fillStyle = '#4a90e2'
    this.ctx.fillRect(barX, barY, barWidth * progress, barHeight)

    // 绘制进度文字
    const unlockedCount = this.levels.filter(level => level.isUnlocked).length
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(`${unlockedCount}/${this.levels.length}`, windowWidth / 2, barY + 20)
  }

  /**
   * 获取难度颜色
   */
  getDifficultyColor(difficulty) {
    switch (difficulty) {
      case 'easy': return '#4caf50'
      case 'normal': return '#2196f3'
      case 'hard': return '#ff9800'
      case 'expert': return '#f44336'
      default: return '#9e9e9e'
    }
  }

  /**
   * 获取难度文字
   */
  getDifficultyText(difficulty) {
    switch (difficulty) {
      case 'easy': return '简单'
      case 'normal': return '普通'
      case 'hard': return '困难'
      case 'expert': return '专家'
      default: return '未知'
    }
  }

  /**
   * 调整颜色亮度
   */
  adjustColor(color, amount) {
    const hex = color.replace('#', '')
    const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount))
    const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount))
    const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount))
    return `rgb(${r}, ${g}, ${b})`
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath()
    this.ctx.moveTo(x + radius, y)
    this.ctx.lineTo(x + width - radius, y)
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    this.ctx.lineTo(x + width, y + height - radius)
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    this.ctx.lineTo(x + radius, y + height)
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    this.ctx.lineTo(x, y + radius)
    this.ctx.quadraticCurveTo(x, y, x + radius, y)
    this.ctx.closePath()
  }

  /**
   * 销毁场景
   */
  destroy() {
    // 清理触摸事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchMoveHandler) {
      wx.offTouchMove(this.touchMoveHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    // 清理其他资源
    this.touchStartHandler = null
    this.touchMoveHandler = null
    this.touchEndHandler = null
  }
}

// 导出模块
module.exports = AdventureMapScene
