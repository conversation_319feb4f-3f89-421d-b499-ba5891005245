/**
 * 微信小游戏适配器
 * 简化版本，专门针对微信小游戏环境
 */

// 检查是否在微信小游戏环境中
if (typeof wx !== 'undefined' && typeof GameGlobal !== 'undefined') {
  console.log('微信小游戏环境检测成功')

  // 基础适配
  const global = GameGlobal || this

  // 简单的全局对象设置
  if (typeof global !== 'undefined') {
    // 确保基础对象存在
    global.console = console
    global.setTimeout = setTimeout
    global.clearTimeout = clearTimeout
    global.setInterval = setInterval
    global.clearInterval = clearInterval
    global.requestAnimationFrame = requestAnimationFrame
    global.cancelAnimationFrame = cancelAnimationFrame
  }

  // 本地存储适配
  global.localStorage = {
    getItem(key) {
      try {
        return wx.getStorageSync(key)
      } catch (e) {
        console.warn('localStorage.getItem error:', e)
        return null
      }
    },
    setItem(key, value) {
      try {
        wx.setStorageSync(key, value)
      } catch (e) {
        console.warn('localStorage.setItem error:', e)
      }
    },
    removeItem(key) {
      try {
        wx.removeStorageSync(key)
      } catch (e) {
        console.warn('localStorage.removeItem error:', e)
      }
    },
    clear() {
      try {
        wx.clearStorageSync()
      } catch (e) {
        console.warn('localStorage.clear error:', e)
      }
    }
  }

  console.log('微信小游戏适配器加载完成')
} else {
  console.warn('未检测到微信小游戏环境')
}
