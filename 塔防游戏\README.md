# 塔防英雄 - 微信小游戏

一个横屏塔防小游戏，支持多种手机屏幕适配。

## 功能特性

- 🎮 横屏游戏体验
- 📱 多屏幕尺寸适配
- 🎨 精美的登录界面
- ⚡ 流畅的动画效果
- 🔧 完整的微信小游戏框架

## 项目结构

```
塔防游戏/
├── game.js                 # 游戏入口文件
├── game.json              # 游戏配置文件
├── project.config.json    # 项目配置文件
├── js/
│   ├── main.js           # 主程序
│   ├── scenes/
│   │   └── LoginScene.js # 登录场景
│   ├── utils/
│   │   └── ScreenAdapter.js # 屏幕适配工具
│   └── libs/
│       ├── weapp-adapter.js # 微信适配器
│       └── symbol.js        # Symbol polyfill
└── README.md
```

## 屏幕适配

游戏支持以下屏幕适配功能：

1. **多分辨率适配**: 自动适配不同手机屏幕尺寸
2. **横屏优化**: 专为横屏游戏设计
3. **刘海屏支持**: 自动检测并适配刘海屏设备
4. **安全区域**: 确保UI元素在安全区域内显示

## 开发说明

### 设计尺寸
- 基准设计尺寸: 1334x750 (横屏)
- 支持等比缩放适配各种屏幕

### 坐标系统
- 使用设计坐标系进行开发
- 自动转换为实际屏幕坐标

### 触摸事件
- 支持多点触控
- 自动转换触摸坐标到设计坐标系

## 使用方法

1. 在微信开发者工具中打开项目
2. 选择"小游戏"项目类型
3. 导入项目目录
4. 点击"编译"运行游戏

## 技术栈

- 微信小游戏 API
- Canvas 2D 渲染
- ES6+ JavaScript
- 模块化开发

## 版本信息

- 版本: v1.0.0
- 更新日期: 2025-07-28
- 支持平台: 微信小游戏
