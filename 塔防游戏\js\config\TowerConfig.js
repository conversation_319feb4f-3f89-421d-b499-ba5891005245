/**
 * 塔配置系统
 * 定义所有塔的属性和升级数据
 */

const TowerConfig = {
  
  /**
   * 箭塔 - 基础远程攻击塔
   */
  arrow_tower: {
    id: "arrow_tower",
    name: "箭塔",
    description: "基础远程攻击塔，攻击速度快",
    type: "physical",
    
    // 等级配置 (1-3级)
    levels: [
      {
        level: 1,
        damage: 25,
        range: 100,
        attackSpeed: 1.2,  // 每秒攻击次数
        cost: 50,
        sellValue: 35
      },
      {
        level: 2,
        damage: 40,
        range: 120,
        attackSpeed: 1.4,
        cost: 75,
        sellValue: 85
      },
      {
        level: 3,
        damage: 65,
        range: 140,
        attackSpeed: 1.6,
        cost: 100,
        sellValue: 150
      }
    ],
    
    // 视觉配置
    visual: {
      size: 32,
      color: "#8B4513",
      bulletType: "arrow",
      attackEffect: "muzzle_flash"
    },
    
    // 目标选择策略
    targeting: {
      priority: "closest", // closest, strongest, weakest, first, last
      canTargetAir: false,
      canTargetGround: true
    },
    
    // 特殊能力
    abilities: []
  },

  /**
   * 炮塔 - 高伤害溅射塔
   */
  cannon_tower: {
    id: "cannon_tower",
    name: "炮塔",
    description: "高伤害攻击，具有溅射效果",
    type: "physical",
    
    levels: [
      {
        level: 1,
        damage: 80,
        range: 90,
        attackSpeed: 0.6,
        cost: 100,
        sellValue: 70,
        splashRadius: 40,
        splashDamage: 0.5  // 溅射伤害比例
      },
      {
        level: 2,
        damage: 130,
        range: 100,
        attackSpeed: 0.7,
        cost: 150,
        sellValue: 175,
        splashRadius: 50,
        splashDamage: 0.6
      },
      {
        level: 3,
        damage: 200,
        range: 110,
        attackSpeed: 0.8,
        cost: 200,
        sellValue: 300,
        splashRadius: 60,
        splashDamage: 0.7
      }
    ],
    
    visual: {
      size: 36,
      color: "#696969",
      bulletType: "cannonball",
      attackEffect: "explosion"
    },
    
    targeting: {
      priority: "strongest",
      canTargetAir: false,
      canTargetGround: true
    },
    
    abilities: ["splash_damage"]
  },

  /**
   * 魔法塔 - 魔法伤害和减速
   */
  magic_tower: {
    id: "magic_tower",
    name: "魔法塔",
    description: "造成魔法伤害，可以减速敌人",
    type: "magic",
    
    levels: [
      {
        level: 1,
        damage: 35,
        range: 110,
        attackSpeed: 1.0,
        cost: 80,
        sellValue: 55,
        slowEffect: 0.3,  // 减速30%
        slowDuration: 2000  // 持续2秒
      },
      {
        level: 2,
        damage: 55,
        range: 125,
        attackSpeed: 1.1,
        cost: 120,
        sellValue: 140,
        slowEffect: 0.4,
        slowDuration: 2500
      },
      {
        level: 3,
        damage: 85,
        range: 140,
        attackSpeed: 1.2,
        cost: 160,
        sellValue: 240,
        slowEffect: 0.5,
        slowDuration: 3000
      }
    ],
    
    visual: {
      size: 34,
      color: "#9370DB",
      bulletType: "magic_bolt",
      attackEffect: "magic_circle"
    },
    
    targeting: {
      priority: "first",
      canTargetAir: true,
      canTargetGround: true
    },
    
    abilities: ["slow_effect", "magic_damage"]
  },

  /**
   * 冰塔 - 冰冻和减速
   */
  ice_tower: {
    id: "ice_tower",
    name: "冰塔",
    description: "冰冻敌人，大幅降低移动速度",
    type: "ice",
    
    levels: [
      {
        level: 1,
        damage: 20,
        range: 95,
        attackSpeed: 0.8,
        cost: 90,
        sellValue: 65,
        freezeChance: 0.2,  // 20%几率冰冻
        freezeDuration: 1500,
        slowEffect: 0.6,
        slowDuration: 3000
      },
      {
        level: 2,
        damage: 35,
        range: 105,
        attackSpeed: 0.9,
        cost: 135,
        sellValue: 155,
        freezeChance: 0.3,
        freezeDuration: 2000,
        slowEffect: 0.7,
        slowDuration: 3500
      },
      {
        level: 3,
        damage: 55,
        range: 115,
        attackSpeed: 1.0,
        cost: 180,
        sellValue: 270,
        freezeChance: 0.4,
        freezeDuration: 2500,
        slowEffect: 0.8,
        slowDuration: 4000
      }
    ],
    
    visual: {
      size: 32,
      color: "#87CEEB",
      bulletType: "ice_shard",
      attackEffect: "frost_nova"
    },
    
    targeting: {
      priority: "first",
      canTargetAir: false,
      canTargetGround: true
    },
    
    abilities: ["freeze_effect", "slow_effect"]
  },

  /**
   * 毒塔 - 持续伤害
   */
  poison_tower: {
    id: "poison_tower",
    name: "毒塔",
    description: "造成持续毒素伤害，降低敌人护甲",
    type: "poison",
    
    levels: [
      {
        level: 1,
        damage: 15,
        range: 85,
        attackSpeed: 1.5,
        cost: 70,
        sellValue: 50,
        poisonDamage: 8,  // 每秒毒素伤害
        poisonDuration: 4000,
        armorReduction: 2
      },
      {
        level: 2,
        damage: 25,
        range: 95,
        attackSpeed: 1.7,
        cost: 105,
        sellValue: 120,
        poisonDamage: 12,
        poisonDuration: 5000,
        armorReduction: 4
      },
      {
        level: 3,
        damage: 40,
        range: 105,
        attackSpeed: 1.9,
        cost: 140,
        sellValue: 210,
        poisonDamage: 18,
        poisonDuration: 6000,
        armorReduction: 6
      }
    ],
    
    visual: {
      size: 30,
      color: "#9ACD32",
      bulletType: "poison_dart",
      attackEffect: "poison_cloud"
    },
    
    targeting: {
      priority: "strongest",
      canTargetAir: false,
      canTargetGround: true
    },
    
    abilities: ["poison_damage", "armor_reduction"]
  }
}

/**
 * 获取塔配置
 */
function getTowerConfig(towerId) {
  return TowerConfig[towerId] || null
}

/**
 * 获取塔的等级配置
 */
function getTowerLevelConfig(towerId, level) {
  const config = getTowerConfig(towerId)
  if (!config || level < 1 || level > config.levels.length) {
    return null
  }
  return config.levels[level - 1]
}

/**
 * 获取所有塔类型
 */
function getAllTowerTypes() {
  return Object.keys(TowerConfig)
}

/**
 * 计算塔的总升级费用
 */
function getTotalUpgradeCost(towerId, targetLevel) {
  const config = getTowerConfig(towerId)
  if (!config || targetLevel < 1 || targetLevel > config.levels.length) {
    return 0
  }
  
  let totalCost = 0
  for (let i = 0; i < targetLevel; i++) {
    totalCost += config.levels[i].cost
  }
  return totalCost
}

/**
 * 获取下一级升级费用
 */
function getUpgradeCost(towerId, currentLevel) {
  const config = getTowerConfig(towerId)
  if (!config || currentLevel >= config.levels.length) {
    return null // 已达到最高级
  }
  return config.levels[currentLevel].cost
}

/**
 * 检查塔是否可以升级
 */
function canUpgrade(towerId, currentLevel) {
  const config = getTowerConfig(towerId)
  return config && currentLevel < config.levels.length
}

// 导出模块
module.exports = {
  TowerConfig,
  getTowerConfig,
  getTowerLevelConfig,
  getAllTowerTypes,
  getTotalUpgradeCost,
  getUpgradeCost,
  canUpgrade
}
