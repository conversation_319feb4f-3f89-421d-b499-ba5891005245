# 塔防游戏战斗系统测试指南

## 🎯 测试目标

验证新实现的塔防游戏战斗系统是否正常工作，包括：
- 基础实体系统
- 战斗逻辑
- UI交互
- 地图系统
- 性能表现

## 📋 测试准备

### 1. 环境要求
- 微信开发者工具
- 小游戏项目类型
- 建议使用真机调试

### 2. 文件检查
确认以下文件已正确创建：
- ✅ `js/entities/BaseEntity.js` - 基础实体类
- ✅ `js/entities/Tower.js` - 塔类
- ✅ `js/entities/Enemy.js` - 敌人类
- ✅ `js/entities/Bullet.js` - 子弹类
- ✅ `js/systems/EntityManager.js` - 实体管理器
- ✅ `js/systems/GameMap.js` - 地图系统
- ✅ `js/systems/CombatSystem.js` - 战斗系统
- ✅ `js/ui/BattleUI.js` - 战斗界面
- ✅ `js/scenes/BattleScene.js` - 战斗场景
- ✅ `js/config/LevelConfig.js` - 关卡配置
- ✅ `js/config/TowerConfig.js` - 塔配置
- ✅ `js/config/EnemyConfig.js` - 敌人配置

## 🚀 测试步骤

### 第一阶段：基础功能测试

#### 1. 启动游戏
1. 在微信开发者工具中打开项目
2. 点击"编译"按钮
3. 验证游戏正常启动到登录页面

#### 2. 进入战斗场景
1. 点击"开始游戏"
2. 点击"挑战"进入挑战页面
3. 点击"冒险关卡"进入关卡地图
4. 点击第一个关卡（应该是解锁状态）
5. 在弹出的对话框中点击"开始挑战"
6. 验证是否成功进入战斗场景

**预期结果：**
- 显示地图背景（绿色草地）
- 显示棕色路径
- 显示游戏HUD（金币、生命值、波次、分数）
- 显示控制按钮（暂停、速度、菜单）

#### 3. 基础UI测试
1. 验证HUD显示：
   - 金币：200
   - 生命值：20
   - 波次：1/3
   - 分数：0

2. 测试控制按钮：
   - 点击暂停按钮，验证游戏暂停
   - 点击速度按钮，验证速度切换（1x ↔ 2x）
   - 点击菜单按钮，验证弹出选项

### 第二阶段：建造系统测试

#### 4. 建造塔测试
1. 点击地图上的空白区域
2. 验证是否弹出建造菜单
3. 选择"箭塔"（费用50金币）
4. 验证塔是否成功建造
5. 验证金币是否正确扣除（200 → 150）

**预期结果：**
- 建造菜单正确显示
- 塔成功放置在点击位置
- 塔显示等级1
- 金币正确扣除

#### 5. 塔选择和信息测试
1. 点击已建造的塔
2. 验证塔是否被选中（白色边框）
3. 验证是否显示攻击范围（半透明圆圈）
4. 验证是否弹出塔信息面板
5. 检查面板显示的信息是否正确

#### 6. 塔升级测试
1. 选中一个塔
2. 点击信息面板中的"升级"按钮
3. 验证升级是否成功
4. 验证塔等级是否变为2
5. 验证金币是否正确扣除

#### 7. 塔出售测试
1. 选中一个塔
2. 点击信息面板中的"出售"按钮
3. 确认出售
4. 验证塔是否被移除
5. 验证金币是否正确增加

### 第三阶段：战斗系统测试

#### 8. 敌人生成测试
1. 等待游戏开始后几秒
2. 验证敌人是否从路径起点生成
3. 验证敌人是否沿着路径移动
4. 观察敌人的移动速度和动画

**预期结果：**
- 敌人从绿色圆圈（起点）生成
- 敌人沿着棕色路径移动
- 敌人有移动动画效果

#### 9. 攻击系统测试
1. 建造一个箭塔在敌人路径附近
2. 等待敌人进入塔的攻击范围
3. 验证塔是否自动攻击敌人
4. 验证是否有子弹飞向敌人
5. 验证敌人是否受到伤害（血条减少）

**预期结果：**
- 塔自动朝向敌人
- 发射箭矢子弹
- 敌人血量减少
- 敌人死亡后消失并给予金币奖励

#### 10. 波次系统测试
1. 消灭第一波所有敌人
2. 验证是否自动开始第二波
3. 观察敌人数量和类型的变化
4. 验证波次计数器是否正确更新

### 第四阶段：高级功能测试

#### 11. 多种塔类型测试
1. 建造不同类型的塔：
   - 箭塔（基础攻击）
   - 炮塔（溅射伤害）
   - 魔法塔（减速效果）
   - 冰塔（冰冻效果）

2. 验证每种塔的特殊效果：
   - 炮塔的爆炸效果
   - 魔法塔的减速效果（敌人变慢）
   - 冰塔的冰冻效果（敌人停止移动）

#### 12. 敌人类型测试
观察不同波次中出现的敌人类型：
- 士兵（基础敌人）
- 弓箭手（快速移动）
- 重甲兵（高血量，慢速）

#### 13. 胜负条件测试

**胜利测试：**
1. 成功消灭所有波次的敌人
2. 验证是否显示胜利对话框
3. 验证奖励是否正确显示

**失败测试：**
1. 让敌人到达终点直到生命值归零
2. 验证是否显示失败对话框
3. 验证重新挑战和返回选项

### 第五阶段：性能和稳定性测试

#### 14. 性能测试
1. 建造多个塔（10个以上）
2. 观察游戏是否保持流畅
3. 检查FPS是否稳定在50-60
4. 长时间游戏测试（5分钟以上）

#### 15. 内存测试
1. 多次进入和退出战斗场景
2. 验证是否有内存泄漏
3. 检查游戏是否越来越卡顿

#### 16. 边界情况测试
1. 尝试在路径上建造塔（应该失败）
2. 金币不足时尝试建造塔
3. 尝试升级已达最高级的塔
4. 快速连续点击测试

## 📊 测试结果记录

### 基础功能 ✅/❌
- [ ] 游戏启动正常
- [ ] 场景切换正常
- [ ] UI显示正确
- [ ] 控制按钮工作

### 建造系统 ✅/❌
- [ ] 建造菜单显示
- [ ] 塔建造成功
- [ ] 塔选择功能
- [ ] 塔升级功能
- [ ] 塔出售功能

### 战斗系统 ✅/❌
- [ ] 敌人生成正常
- [ ] 敌人移动正常
- [ ] 塔攻击功能
- [ ] 伤害计算正确
- [ ] 波次系统正常

### 高级功能 ✅/❌
- [ ] 多种塔类型
- [ ] 特殊效果正常
- [ ] 多种敌人类型
- [ ] 胜负判定正确

### 性能表现 ✅/❌
- [ ] 帧率稳定
- [ ] 内存使用正常
- [ ] 长时间运行稳定
- [ ] 边界情况处理

## 🐛 已知问题

### 可能遇到的问题
1. **配置文件错误**：检查config文件夹中的配置是否正确
2. **路径问题**：确认require路径是否正确
3. **事件绑定**：检查触摸事件是否正确绑定
4. **内存问题**：注意对象池的使用和清理

### 调试技巧
1. 使用console.log输出调试信息
2. 在BattleScene中启用调试信息显示
3. 检查微信开发者工具的控制台错误
4. 使用真机调试测试性能

## 🎯 成功标准

战斗系统测试成功的标准：
- 所有基础功能正常工作
- 建造和战斗逻辑正确
- UI交互响应良好
- 性能稳定，FPS > 50
- 无明显内存泄漏
- 游戏体验流畅

通过这些测试后，战斗系统就可以投入使用了！
