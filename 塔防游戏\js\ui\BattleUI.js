/**
 * 战斗界面UI系统
 * 管理游戏内的所有UI交互
 */

const ScreenAdapter = require('../utils/ScreenAdapter.js')

class BattleUI {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // UI状态
    this.isVisible = true
    this.isPaused = false
    this.gameSpeed = 1  // 1x, 2x
    
    // 当前选中的塔
    this.selectedTower = null
    
    // UI面板
    this.panels = {
      gameHUD: null,      // 游戏信息面板
      buildMenu: null,    // 建造菜单
      towerInfo: null,    // 塔信息面板
      pauseMenu: null     // 暂停菜单
    }
    
    // 按钮配置
    this.buttons = {}
    
    // 初始化UI
    this.initializeUI()
    
    // 绑定事件
    this.bindEvents()
  }
  
  /**
   * 初始化UI元素
   */
  initializeUI() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()

    // 游戏信息HUD
    this.panels.gameHUD = {
      x: 10,
      y: 10,
      width: 200,
      height: 80,
      elements: {
        gold: { x: 20, y: 30, value: 0, animation: 0 },
        lives: { x: 20, y: 50, value: 20, animation: 0 },
        wave: { x: 120, y: 30, current: 1, total: 10, animation: 0 },
        score: { x: 120, y: 50, value: 0, animation: 0 }
      }
    }
    
    // 控制按钮
    this.buttons = {
      pause: {
        x: windowWidth - 60,
        y: 30,
        width: 40,
        height: 40,
        text: '⏸️',
        action: 'pause'
      },
      speed: {
        x: windowWidth - 110,
        y: 30,
        width: 40,
        height: 40,
        text: '1x',
        action: 'speed'
      },
      menu: {
        x: windowWidth - 160,
        y: 30,
        width: 40,
        height: 40,
        text: '☰',
        action: 'menu'
      }
    }
    
    // 建造菜单（初始隐藏）
    this.panels.buildMenu = {
      x: 0,
      y: 0,
      width: 300,
      height: 260, // 增加高度以容纳5个塔选项
      isVisible: false,
      towers: [
        { id: 'arrow_tower', name: '箭塔', cost: 50, icon: '🏹' },
        { id: 'cannon_tower', name: '炮塔', cost: 100, icon: '💣' },
        { id: 'magic_tower', name: '魔法塔', cost: 80, icon: '🔮' },
        { id: 'ice_tower', name: '冰塔', cost: 90, icon: '❄️' },
        { id: 'poison_tower', name: '毒塔', cost: 70, icon: '☠️' }
      ]
    }
    
    // 塔信息面板（初始隐藏）
    this.panels.towerInfo = {
      x: 0,
      y: 0,
      width: 250,
      height: 180,
      isVisible: false,
      tower: null
    }
  }
  
  /**
   * 绑定触摸事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    
    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 处理触摸事件
   * @param {Object} pos - 触摸位置
   * @returns {boolean} - 是否处理了事件
   */
  handleTouch(pos) {
    // 检查按钮点击
    for (let buttonId in this.buttons) {
      const button = this.buttons[buttonId]
      if (this.isPointInRect(pos, button)) {
        this.handleButtonClick(buttonId)
        return true
      }
    }

    // 检查建造菜单
    if (this.panels.buildMenu.isVisible) {
      return this.checkBuildMenuClick(pos)
    }

    // 检查塔信息面板
    if (this.panels.towerInfo.isVisible) {
      return this.checkTowerInfoClick(pos)
    }

    return false
  }

  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    this.handleTouch(pos)
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 处理按钮点击
    for (let buttonId in this.buttons) {
      const button = this.buttons[buttonId]
      if (button.isPressed && this.isPointInRect(pos, button)) {
        this.handleButtonClick(button.action)
      }
      button.isPressed = false
    }
  }
  
  /**
   * 处理按钮点击
   */
  handleButtonClick(action) {
    switch (action) {
      case 'pause':
        this.togglePause()
        break
      case 'speed':
        this.toggleSpeed()
        break
      case 'menu':
        this.showPauseMenu()
        break
    }
  }
  
  /**
   * 显示建造菜单
   */
  showBuildMenu(x, y) {
    this.panels.buildMenu.x = x - this.panels.buildMenu.width / 2
    this.panels.buildMenu.y = y - this.panels.buildMenu.height / 2
    this.panels.buildMenu.isVisible = true

    // 隐藏其他面板
    this.panels.towerInfo.isVisible = false
  }
  
  /**
   * 隐藏建造菜单
   */
  hideBuildMenu() {
    this.panels.buildMenu.isVisible = false
  }
  
  /**
   * 显示塔信息面板
   */
  showTowerInfo(tower) {
    this.selectedTower = tower
    this.panels.towerInfo.tower = tower
    this.panels.towerInfo.x = tower.x + 50
    this.panels.towerInfo.y = tower.y - this.panels.towerInfo.height / 2
    this.panels.towerInfo.isVisible = true
    
    // 隐藏其他面板
    this.panels.buildMenu.isVisible = false
  }
  
  /**
   * 隐藏塔信息面板
   */
  hideTowerInfo() {
    this.panels.towerInfo.isVisible = false
    this.selectedTower = null
  }
  
  /**
   * 检查建造菜单点击
   */
  checkBuildMenuClick(pos) {
    const menu = this.panels.buildMenu
    if (!this.isPointInRect(pos, menu)) {
      this.hideBuildMenu()
      return true // 处理了点击事件
    }

    // 检查塔类型选择
    const towerHeight = 40
    const startY = menu.y + 30

    for (let index = 0; index < menu.towers.length; index++) {
      const tower = menu.towers[index]
      const towerRect = {
        x: menu.x + 10,
        y: startY + index * (towerHeight + 5),
        width: menu.width - 20,
        height: towerHeight
      }

      if (this.isPointInRect(pos, towerRect)) {
        if (this.onTowerSelect) {
          this.onTowerSelect(tower)
        }
        return true // 处理了点击事件
      }
    }

    return true // 在菜单内点击，阻止其他处理
  }
  
  /**
   * 检查塔信息面板点击
   */
  checkTowerInfoClick(pos) {
    const panel = this.panels.towerInfo
    if (!this.isPointInRect(pos, panel)) {
      this.hideTowerInfo()
      return true // 处理了点击事件
    }

    // 检查升级按钮
    const upgradeButton = {
      x: panel.x + 20,
      y: panel.y + panel.height - 50,
      width: 80,
      height: 30
    }

    if (this.isPointInRect(pos, upgradeButton)) {
      if (this.onTowerUpgrade && this.selectedTower) {
        // 检查是否可以升级
        const tower = this.selectedTower
        const canUpgrade = tower.level < 3
        const upgradeCost = tower.getUpgradeCost()
        const hasEnoughGold = this.gameData.gold >= upgradeCost

        if (canUpgrade && hasEnoughGold) {
          this.onTowerUpgrade(this.selectedTower)
        }
      }
      return true
    }

    // 检查出售按钮
    const sellButton = {
      x: panel.x + 120,
      y: panel.y + panel.height - 50,
      width: 80,
      height: 30
    }

    if (this.isPointInRect(pos, sellButton)) {
      if (this.onTowerSell) {
        this.onTowerSell(this.selectedTower)
      }
      return true
    }

    return true // 在面板内点击，阻止其他处理
  }
  
  /**
   * 切换暂停状态
   */
  togglePause() {
    this.isPaused = !this.isPaused
    this.buttons.pause.text = this.isPaused ? '▶️' : '⏸️'
    
    if (this.onPauseToggle) {
      this.onPauseToggle(this.isPaused)
    }
  }
  
  /**
   * 切换游戏速度
   */
  toggleSpeed() {
    this.gameSpeed = this.gameSpeed === 1 ? 2 : 1
    this.buttons.speed.text = `${this.gameSpeed}x`
    
    if (this.onSpeedChange) {
      this.onSpeedChange(this.gameSpeed)
    }
  }
  
  /**
   * 显示暂停菜单
   */
  showPauseMenu() {
    if (this.onMenuShow) {
      this.onMenuShow()
    }
  }
  
  /**
   * 更新游戏数据
   */
  updateGameData(data) {
    if (data.gold !== undefined) {
      const oldValue = this.panels.gameHUD.elements.gold.value
      this.panels.gameHUD.elements.gold.value = data.gold
      // 如果金币增加，触发动画
      if (data.gold > oldValue) {
        this.panels.gameHUD.elements.gold.animation = 1
      }
    }
    if (data.lives !== undefined) {
      const oldValue = this.panels.gameHUD.elements.lives.value
      this.panels.gameHUD.elements.lives.value = data.lives
      // 如果生命值减少，触发动画
      if (data.lives < oldValue) {
        this.panels.gameHUD.elements.lives.animation = 1
      }
    }
    if (data.wave !== undefined) {
      const oldWave = this.panels.gameHUD.elements.wave.current
      this.panels.gameHUD.elements.wave.current = data.wave.current
      this.panels.gameHUD.elements.wave.total = data.wave.total
      // 如果波次增加，触发动画
      if (data.wave.current > oldWave) {
        this.panels.gameHUD.elements.wave.animation = 1
      }
    }
    if (data.score !== undefined) {
      const oldValue = this.panels.gameHUD.elements.score.value
      this.panels.gameHUD.elements.score.value = data.score
      // 如果分数增加，触发动画
      if (data.score > oldValue) {
        this.panels.gameHUD.elements.score.animation = 1
      }
    }
  }
  
  /**
   * 检查点是否在矩形内
   */
  isPointInRect(point, rect) {
    return point.x >= rect.x && 
           point.x <= rect.x + rect.width &&
           point.y >= rect.y && 
           point.y <= rect.y + rect.height
  }
  
  /**
   * 更新UI动画
   */
  update(deltaTime) {
    // 更新HUD动画
    const hud = this.panels.gameHUD.elements
    for (const key in hud) {
      if (hud[key].animation > 0) {
        hud[key].animation -= deltaTime / 500  // 动画持续0.5秒
        if (hud[key].animation < 0) {
          hud[key].animation = 0
        }
      }
    }
  }

  /**
   * 渲染UI
   */
  render() {
    if (!this.isVisible) return

    this.ctx.save()

    // 绘制游戏HUD
    this.renderGameHUD()

    // 绘制控制按钮
    this.renderButtons()

    // 绘制建造菜单
    if (this.panels.buildMenu.isVisible) {
      this.renderBuildMenu()
    }

    // 绘制塔信息面板
    if (this.panels.towerInfo.isVisible) {
      this.renderTowerInfo()
    }

    this.ctx.restore()
  }

  /**
   * 渲染游戏HUD
   */
  renderGameHUD() {
    const hud = this.panels.gameHUD

    // 背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(hud.x, hud.y, hud.width, hud.height)

    // 边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 1
    this.ctx.strokeRect(hud.x, hud.y, hud.width, hud.height)

    // 文字
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'middle'

    // 金币
    this.renderHUDElement(
      `💰 ${hud.elements.gold.value}`,
      hud.x + hud.elements.gold.x,
      hud.y + hud.elements.gold.y,
      hud.elements.gold.animation,
      '#ffff00'
    )

    // 生命值
    this.renderHUDElement(
      `❤️ ${hud.elements.lives.value}`,
      hud.x + hud.elements.lives.x,
      hud.y + hud.elements.lives.y,
      hud.elements.lives.animation,
      '#ff0000'
    )

    // 波次
    this.renderHUDElement(
      `🌊 ${hud.elements.wave.current}/${hud.elements.wave.total}`,
      hud.x + hud.elements.wave.x,
      hud.y + hud.elements.wave.y,
      hud.elements.wave.animation,
      '#00ffff'
    )

    // 分数
    this.renderHUDElement(
      `⭐ ${hud.elements.score.value}`,
      hud.x + hud.elements.score.x,
      hud.y + hud.elements.score.y,
      hud.elements.score.animation,
      '#00ff00'
    )
  }

  /**
   * 渲染HUD元素（带动画效果）
   */
  renderHUDElement(text, x, y, animation, highlightColor) {
    this.ctx.save()

    // 如果有动画，应用缩放和颜色变化
    if (animation > 0) {
      const scale = 1 + animation * 0.5
      this.ctx.translate(x, y)
      this.ctx.scale(scale, scale)
      this.ctx.fillStyle = highlightColor
      this.ctx.font = 'bold 14px Arial'
      this.ctx.fillText(text, 0, 0)
    } else {
      this.ctx.fillStyle = '#ffffff'
      this.ctx.font = '14px Arial'
      this.ctx.fillText(text, x, y)
    }

    this.ctx.restore()
  }

  /**
   * 渲染控制按钮
   */
  renderButtons() {
    for (let buttonId in this.buttons) {
      const button = this.buttons[buttonId]
      this.renderButton(button)
    }
  }

  /**
   * 渲染单个按钮
   */
  renderButton(button) {
    // 背景
    this.ctx.fillStyle = button.isPressed ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 文字
    this.ctx.fillStyle = button.isPressed ? '#000000' : '#ffffff'
    this.ctx.font = '16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text,
      button.x + button.width / 2,
      button.y + button.height / 2)
  }

  /**
   * 渲染建造菜单
   */
  renderBuildMenu() {
    const menu = this.panels.buildMenu

    // 背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.9)'
    this.ctx.fillRect(menu.x, menu.y, menu.width, menu.height)

    // 边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(menu.x, menu.y, menu.width, menu.height)

    // 标题
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('选择塔类型', menu.x + menu.width / 2, menu.y + 15)

    // 塔选项
    const towerHeight = 40
    const startY = menu.y + 30

    menu.towers.forEach((tower, index) => {
      const y = startY + index * (towerHeight + 5)

      // 塔背景
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)'
      this.ctx.fillRect(menu.x + 10, y, menu.width - 20, towerHeight)

      // 塔图标
      this.ctx.font = '24px Arial'
      this.ctx.textAlign = 'left'
      this.ctx.fillText(tower.icon, menu.x + 20, y + towerHeight / 2)

      // 塔名称
      this.ctx.font = '14px Arial'
      this.ctx.fillStyle = '#ffffff'
      this.ctx.fillText(tower.name, menu.x + 60, y + towerHeight / 2 - 5)

      // 塔费用
      this.ctx.fillStyle = '#ffff00'
      this.ctx.fillText(`💰${tower.cost}`, menu.x + 60, y + towerHeight / 2 + 10)

      // 边框
      this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(menu.x + 10, y, menu.width - 20, towerHeight)
    })
  }

  /**
   * 渲染塔信息面板
   */
  renderTowerInfo() {
    const panel = this.panels.towerInfo
    const tower = panel.tower

    if (!tower) return

    // 背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.9)'
    this.ctx.fillRect(panel.x, panel.y, panel.width, panel.height)

    // 边框
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(panel.x, panel.y, panel.width, panel.height)

    // 塔名称
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(tower.name || '塔', panel.x + panel.width / 2, panel.y + 20)

    // 塔属性
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'left'

    const startY = panel.y + 45
    const lineHeight = 16

    // 基础属性
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillText(`等级: ${tower.level || 1}/3`, panel.x + 20, startY)
    this.ctx.fillText(`伤害: ${Math.round(tower.damage || 0)}`, panel.x + 20, startY + lineHeight)
    this.ctx.fillText(`射程: ${Math.round(tower.range || 0)}`, panel.x + 20, startY + lineHeight * 2)
    this.ctx.fillText(`攻速: ${(tower.attackSpeed || 1000)}ms`, panel.x + 20, startY + lineHeight * 3)

    // 特殊属性
    if (tower.towerType === 'ice_tower') {
      this.ctx.fillStyle = '#87ceeb'
      this.ctx.fillText(`减速: ${Math.round((tower.slowEffect || 0.5) * 100)}%`, panel.x + 20, startY + lineHeight * 4)
    } else if (tower.towerType === 'poison_tower') {
      this.ctx.fillStyle = '#9acd32'
      this.ctx.fillText(`毒伤: ${Math.round(tower.poisonDamage || 0)}/s`, panel.x + 20, startY + lineHeight * 4)
    } else if (tower.towerType === 'cannon_tower') {
      this.ctx.fillStyle = '#ff6600'
      this.ctx.fillText(`范围: ${Math.round(tower.splashRadius || 0)}`, panel.x + 20, startY + lineHeight * 4)
    }

    // 升级信息
    if (tower.level < 3) {
      const upgradeCost = tower.getUpgradeCost()
      this.ctx.fillStyle = '#ffff00'
      this.ctx.fillText(`升级费用: ${upgradeCost}`, panel.x + 20, startY + lineHeight * 5.5)
    } else {
      this.ctx.fillStyle = '#ffd700'
      this.ctx.fillText('已满级', panel.x + 20, startY + lineHeight * 5.5)
    }

    // 升级按钮
    const upgradeButton = {
      x: panel.x + 20,
      y: panel.y + panel.height - 50,
      width: 80,
      height: 30
    }

    const canUpgrade = tower.level < 3
    const upgradeCost = tower.getUpgradeCost()
    const hasEnoughGold = this.gameData.gold >= upgradeCost

    // 按钮颜色根据状态变化
    if (!canUpgrade) {
      this.ctx.fillStyle = 'rgba(128, 128, 128, 0.7)' // 灰色 - 已满级
    } else if (!hasEnoughGold) {
      this.ctx.fillStyle = 'rgba(255, 128, 0, 0.7)' // 橙色 - 金币不足
    } else {
      this.ctx.fillStyle = 'rgba(0, 255, 0, 0.7)' // 绿色 - 可升级
    }

    this.ctx.fillRect(upgradeButton.x, upgradeButton.y, upgradeButton.width, upgradeButton.height)
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.strokeRect(upgradeButton.x, upgradeButton.y, upgradeButton.width, upgradeButton.height)

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '11px Arial'
    this.ctx.textAlign = 'center'

    let buttonText = '升级'
    if (!canUpgrade) {
      buttonText = '满级'
    } else if (!hasEnoughGold) {
      buttonText = '金币不足'
    }

    this.ctx.fillText(buttonText, upgradeButton.x + upgradeButton.width / 2, upgradeButton.y + upgradeButton.height / 2)

    // 出售按钮
    const sellButton = {
      x: panel.x + 120,
      y: panel.y + panel.height - 50,
      width: 80,
      height: 30
    }

    this.ctx.fillStyle = 'rgba(255, 0, 0, 0.7)'
    this.ctx.fillRect(sellButton.x, sellButton.y, sellButton.width, sellButton.height)
    this.ctx.strokeStyle = '#ffffff'
    this.ctx.strokeRect(sellButton.x, sellButton.y, sellButton.width, sellButton.height)

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '10px Arial'
    this.ctx.textAlign = 'center'

    const sellValue = tower.getSellValue()
    this.ctx.fillText('出售', sellButton.x + sellButton.width / 2, sellButton.y + 10)
    this.ctx.fillText(`${sellValue}金币`, sellButton.x + sellButton.width / 2, sellButton.y + 22)
  }

  /**
   * 销毁UI
   */
  destroy() {
    // 清理事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    this.touchStartHandler = null
    this.touchEndHandler = null
  }

  // 事件回调 - 需要外部设置
  onTowerSelect(tower) {}
  onTowerUpgrade(tower) {}
  onTowerSell(tower) {}
  onPauseToggle(isPaused) {}
  onSpeedChange(speed) {}
  onMenuShow() {}
}

// 导出模块
module.exports = BattleUI
