/**
 * 特效基类
 * 用于创建各种视觉特效
 */
const BaseEntity = require('./BaseEntity.js')

class Effect extends BaseEntity {
  /**
   * 构造函数
   * @param {number} x - x坐标
   * @param {number} y - y坐标
   * @param {string} effectType - 特效类型
   * @param {Object} config - 特效配置
   */
  constructor(x, y, effectType, config = {}) {
    super(x, y)
    
    this.type = 'effect'
    this.effectType = effectType
    
    // 基础属性
    this.duration = config.duration || 1000
    this.maxDuration = this.duration
    this.scale = config.scale || 1
    this.maxScale = config.maxScale || 2
    this.color = config.color || '#ffffff'
    this.fadeOut = config.fadeOut !== false
    
    // 动画属性
    this.animationSpeed = config.animationSpeed || 1
    this.rotationSpeed = config.rotationSpeed || 0
    this.scaleSpeed = config.scaleSpeed || 0
    
    // 粒子系统属性
    this.particles = []
    this.particleCount = config.particleCount || 0
    
    // 初始化特效
    this.initializeEffect()
  }
  
  /**
   * 初始化特效
   */
  initializeEffect() {
    switch (this.effectType) {
      case 'explosion':
        this.initExplosion()
        break
      case 'hit':
        this.initHitEffect()
        break
      case 'muzzle_flash':
        this.initMuzzleFlash()
        break
      case 'magic_circle':
        this.initMagicCircle()
        break
      case 'frost_nova':
        this.initFrostNova()
        break
      case 'poison_cloud':
        this.initPoisonCloud()
        break
      case 'level_up':
        this.initLevelUpEffect()
        break
      case 'coin_pickup':
        this.initCoinPickup()
        break
    }
  }
  
  /**
   * 初始化爆炸特效
   */
  initExplosion() {
    this.duration = 500
    this.maxScale = 3
    this.scaleSpeed = 4
    this.color = '#ff6600'
    this.particleCount = 8
    
    // 创建爆炸粒子
    for (let i = 0; i < this.particleCount; i++) {
      const angle = (Math.PI * 2 / this.particleCount) * i
      this.particles.push({
        x: 0,
        y: 0,
        vx: Math.cos(angle) * 100,
        vy: Math.sin(angle) * 100,
        life: 1,
        maxLife: 1,
        size: 4,
        color: '#ff6600'
      })
    }
  }
  
  /**
   * 初始化命中特效
   */
  initHitEffect() {
    this.duration = 200
    this.maxScale = 1.5
    this.scaleSpeed = 3
    this.color = '#ffffff'
  }
  
  /**
   * 初始化枪口闪光
   */
  initMuzzleFlash() {
    this.duration = 150
    this.maxScale = 1.2
    this.scaleSpeed = 5
    this.color = '#ffff00'
  }
  
  /**
   * 初始化魔法阵
   */
  initMagicCircle() {
    this.duration = 800
    this.maxScale = 2
    this.rotationSpeed = 5
    this.color = '#9370db'
    this.particleCount = 12
    
    // 创建魔法粒子
    for (let i = 0; i < this.particleCount; i++) {
      const angle = (Math.PI * 2 / this.particleCount) * i
      const radius = 30
      this.particles.push({
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius,
        angle: angle,
        radius: radius,
        life: 1,
        maxLife: 1,
        size: 3,
        color: '#9370db'
      })
    }
  }
  
  /**
   * 初始化冰霜新星
   */
  initFrostNova() {
    this.duration = 600
    this.maxScale = 2.5
    this.color = '#87ceeb'
    this.particleCount = 16
    
    // 创建冰霜粒子
    for (let i = 0; i < this.particleCount; i++) {
      const angle = (Math.PI * 2 / this.particleCount) * i
      this.particles.push({
        x: 0,
        y: 0,
        vx: Math.cos(angle) * 80,
        vy: Math.sin(angle) * 80,
        life: 1,
        maxLife: 1,
        size: 3,
        color: '#87ceeb'
      })
    }
  }
  
  /**
   * 初始化毒云
   */
  initPoisonCloud() {
    this.duration = 1000
    this.maxScale = 2
    this.color = '#9acd32'
    this.particleCount = 20
    
    // 创建毒气粒子
    for (let i = 0; i < this.particleCount; i++) {
      this.particles.push({
        x: (Math.random() - 0.5) * 40,
        y: (Math.random() - 0.5) * 40,
        vx: (Math.random() - 0.5) * 20,
        vy: (Math.random() - 0.5) * 20,
        life: 1,
        maxLife: 1,
        size: Math.random() * 6 + 2,
        color: '#9acd32'
      })
    }
  }
  
  /**
   * 初始化升级特效
   */
  initLevelUpEffect() {
    this.duration = 1000
    this.maxScale = 3
    this.color = '#ffd700'
    this.particleCount = 24
    
    // 创建升级粒子
    for (let i = 0; i < this.particleCount; i++) {
      const angle = (Math.PI * 2 / this.particleCount) * i
      this.particles.push({
        x: 0,
        y: 0,
        vx: Math.cos(angle) * 60,
        vy: Math.sin(angle) * 60 - 50, // 向上飞
        life: 1,
        maxLife: 1,
        size: 4,
        color: '#ffd700'
      })
    }
  }
  
  /**
   * 初始化金币拾取特效
   */
  initCoinPickup() {
    this.duration = 800
    this.color = '#ffd700'
    this.particleCount = 8
    
    // 创建金币粒子
    for (let i = 0; i < this.particleCount; i++) {
      this.particles.push({
        x: (Math.random() - 0.5) * 20,
        y: (Math.random() - 0.5) * 20,
        vx: 0,
        vy: -30 - Math.random() * 20,
        life: 1,
        maxLife: 1,
        size: 3,
        color: '#ffd700'
      })
    }
  }
  
  /**
   * 更新特效
   */
  update(deltaTime) {
    super.update(deltaTime)
    
    // 更新持续时间
    this.duration -= deltaTime
    if (this.duration <= 0) {
      this.destroy()
      return
    }
    
    // 计算进度
    const progress = 1 - (this.duration / this.maxDuration)
    
    // 更新透明度
    if (this.fadeOut) {
      this.alpha = 1 - progress
    }
    
    // 更新缩放
    if (this.scaleSpeed > 0) {
      this.scale = 1 + progress * (this.maxScale - 1)
    }
    
    // 更新旋转
    if (this.rotationSpeed > 0) {
      this.rotation += this.rotationSpeed * deltaTime / 1000
    }
    
    // 更新粒子
    this.updateParticles(deltaTime)
  }
  
  /**
   * 更新粒子
   */
  updateParticles(deltaTime) {
    const dt = deltaTime / 1000
    
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i]
      
      // 更新位置
      particle.x += particle.vx * dt
      particle.y += particle.vy * dt
      
      // 更新生命值
      particle.life -= dt / (this.maxDuration / 1000)
      
      // 特殊更新逻辑
      if (this.effectType === 'magic_circle') {
        // 魔法阵粒子绕圈运动
        particle.angle += 2 * dt
        particle.x = Math.cos(particle.angle) * particle.radius
        particle.y = Math.sin(particle.angle) * particle.radius
      }
      
      // 移除死亡粒子
      if (particle.life <= 0) {
        this.particles.splice(i, 1)
      }
    }
  }
  
  /**
   * 绘制特效
   */
  draw(ctx) {
    // 绘制主特效
    this.drawMainEffect(ctx)
    
    // 绘制粒子
    this.drawParticles(ctx)
  }
  
  /**
   * 绘制主特效
   */
  drawMainEffect(ctx) {
    switch (this.effectType) {
      case 'explosion':
        this.drawExplosion(ctx)
        break
      case 'hit':
        this.drawHit(ctx)
        break
      case 'muzzle_flash':
        this.drawMuzzleFlash(ctx)
        break
      case 'magic_circle':
        this.drawMagicCircle(ctx)
        break
      case 'level_up':
        this.drawLevelUp(ctx)
        break
    }
  }
  
  /**
   * 绘制爆炸
   */
  drawExplosion(ctx) {
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 20 * this.scale)
    gradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)')
    gradient.addColorStop(0.5, 'rgba(255, 102, 0, 0.6)')
    gradient.addColorStop(1, 'rgba(255, 0, 0, 0)')
    
    ctx.fillStyle = gradient
    ctx.beginPath()
    ctx.arc(0, 0, 20 * this.scale, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制命中效果
   */
  drawHit(ctx) {
    ctx.fillStyle = `rgba(255, 255, 255, ${this.alpha})`
    ctx.beginPath()
    ctx.arc(0, 0, 8 * this.scale, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制枪口闪光
   */
  drawMuzzleFlash(ctx) {
    ctx.fillStyle = `rgba(255, 255, 0, ${this.alpha})`
    ctx.beginPath()
    ctx.arc(0, 0, 6 * this.scale, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制魔法阵
   */
  drawMagicCircle(ctx) {
    ctx.strokeStyle = `rgba(147, 112, 219, ${this.alpha})`
    ctx.lineWidth = 2
    
    // 外圈
    ctx.beginPath()
    ctx.arc(0, 0, 25 * this.scale, 0, Math.PI * 2)
    ctx.stroke()
    
    // 内圈
    ctx.beginPath()
    ctx.arc(0, 0, 15 * this.scale, 0, Math.PI * 2)
    ctx.stroke()
  }
  
  /**
   * 绘制升级特效
   */
  drawLevelUp(ctx) {
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 30 * this.scale)
    gradient.addColorStop(0, 'rgba(255, 215, 0, 0.8)')
    gradient.addColorStop(1, 'rgba(255, 215, 0, 0)')
    
    ctx.fillStyle = gradient
    ctx.beginPath()
    ctx.arc(0, 0, 30 * this.scale, 0, Math.PI * 2)
    ctx.fill()
  }
  
  /**
   * 绘制粒子
   */
  drawParticles(ctx) {
    for (const particle of this.particles) {
      const alpha = particle.life / particle.maxLife * this.alpha
      ctx.fillStyle = particle.color.replace('rgb', 'rgba').replace(')', `, ${alpha})`)
      
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fill()
    }
  }
  
  /**
   * 重置特效（用于对象池）
   */
  reset(x, y, effectType, config = {}) {
    this.x = x
    this.y = y
    this.effectType = effectType
    this.isAlive = true
    this.isVisible = true
    this.alpha = 1
    this.scale = config.scale || 1
    this.rotation = 0
    this.particles = []
    
    // 重新初始化
    this.initializeEffect()
  }
}

// 导出模块
module.exports = Effect
