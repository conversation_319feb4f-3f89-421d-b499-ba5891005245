/**
 * 战斗场景
 * 塔防游戏的核心战斗场景
 */
const ScreenAdapter = require('../utils/ScreenAdapter.js')
const EntityManager = require('../systems/EntityManager.js')
const GameMap = require('../systems/GameMap.js')
const { CombatSystem, EffectManager } = require('../systems/CombatSystem.js')
const BattleUI = require('../ui/BattleUI.js')

// 实体类
const Tower = require('../entities/Tower.js')
const Enemy = require('../entities/Enemy.js')
const Bullet = require('../entities/Bullet.js')
const Effect = require('../entities/Effect.js')

// 配置
const LevelConfig = require('../config/LevelConfig.js')
const TowerConfig = require('../config/TowerConfig.js')
const EnemyConfig = require('../config/EnemyConfig.js')

// 工具类
const AudioManager = require('../utils/AudioManager.js')
const DataManager = require('../utils/DataManager.js')
const PerformanceMonitor = require('../utils/PerformanceMonitor.js')

class BattleScene {
  constructor(canvas, ctx, levelId = 1) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 关卡配置
    this.levelId = levelId
    this.levelConfig = LevelConfig.getLevelConfig(levelId)
    
    if (!this.levelConfig) {
      console.error(`找不到关卡配置: ${levelId}`)
      return
    }
    
    // 核心系统
    this.entityManager = new EntityManager()
    this.gameMap = new GameMap(this.levelConfig.map)
    this.combatSystem = new CombatSystem()
    this.effectManager = new EffectManager()
    this.battleUI = new BattleUI(canvas, ctx)
    
    // 连接系统
    this.combatSystem.setEffectManager(this.effectManager)
    this.setupSystemCallbacks()
    
    // 游戏状态
    this.gameState = 'preparing'  // preparing, playing, paused, victory, defeat
    this.gameSpeed = 1
    this.isPaused = false
    
    // 资源
    this.gold = this.levelConfig.initialResources.gold
    this.lives = this.levelConfig.initialResources.lives
    this.score = 0
    
    // 波次管理
    this.currentWave = 0
    this.totalWaves = this.levelConfig.waves.length
    this.waveStartTime = 0
    this.enemySpawnQueue = []
    this.lastEnemySpawnTime = 0
    
    // 选中状态
    this.selectedTower = null
    this.buildMode = false
    this.selectedTowerType = null
    
    // 性能监控
    this.lastFrameTime = 0
    this.fps = 60
    this.frameCount = 0

    // 游戏开始时间（用于统计）
    this.gameStartTime = Date.now()

    // 绑定事件
    this.bindEvents()

    // 初始化游戏
    this.initializeGame()

    // 播放背景音乐
    this.playBackgroundMusic()

    // 记录游戏开始
    DataManager.recordGameStart()

    // 设置性能警告回调
    PerformanceMonitor.setWarningCallback(this.onPerformanceWarning.bind(this))
  }
  
  /**
   * 设置系统回调
   */
  setupSystemCallbacks() {
    // 战斗系统回调
    this.combatSystem.onBulletCreate = this.createBullet.bind(this)
    this.combatSystem.onEnemyKilled = this.onEnemyKilled.bind(this)
    this.combatSystem.onRewardGained = this.onRewardGained.bind(this)
    this.combatSystem.getEnemiesInRadius = this.entityManager.getEnemiesInRadius.bind(this.entityManager)
    
    // UI回调
    this.battleUI.onTowerSelect = this.onTowerSelect.bind(this)
    this.battleUI.onTowerUpgrade = this.onTowerUpgrade.bind(this)
    this.battleUI.onTowerSell = this.onTowerSell.bind(this)
    this.battleUI.onPauseToggle = this.onPauseToggle.bind(this)
    this.battleUI.onSpeedChange = this.onSpeedChange.bind(this)
    this.battleUI.onMenuShow = this.onMenuShow.bind(this)
  }
  
  /**
   * 初始化游戏
   */
  initializeGame() {
    // 从数据管理器加载玩家数据
    const playerData = DataManager.getPlayerData()
    this.gold = Math.max(this.gold, playerData.gold)

    // 更新UI数据
    this.updateUIData()

    // 准备第一波敌人
    this.prepareNextWave()

    // 开始游戏
    this.gameState = 'playing'
  }

  /**
   * 播放背景音乐
   */
  playBackgroundMusic() {
    const settings = DataManager.getSettings()
    if (settings.musicEnabled) {
      AudioManager.playMusic('battle_theme', {
        volume: settings.musicVolume,
        loop: true,
        fadeIn: 1000
      })
    }
  }

  /**
   * 创建特效
   */
  createEffect(x, y, effectType, config = {}) {
    const effect = new Effect(x, y, effectType, config)
    this.entityManager.addEntity(effect)
    return effect
  }

  /**
   * 播放音效
   */
  playSfx(soundId, options = {}) {
    const settings = DataManager.getSettings()
    if (settings.sfxEnabled) {
      AudioManager.playSfx(soundId, {
        volume: settings.sfxVolume,
        ...options
      })
    }
  }
  
  /**
   * 绑定触摸事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    
    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    if (this.isPaused) return

    const touch = e.touches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)

    // 先让UI处理触摸事件
    if (this.battleUI.handleTouch(pos)) {
      return
    }

    // 检查是否点击了塔
    const tower = this.entityManager.getTowerAt(pos.x, pos.y)
    if (tower) {
      this.selectTower(tower)
      return
    }

    // 检查是否可以建造
    if (this.buildMode && this.selectedTowerType) {
      this.tryBuildTower(pos.x, pos.y)
      return
    }

    // 检查是否可以在此位置建造（空地）
    if (this.gameMap.canBuildAt(pos.x, pos.y)) {
      this.showBuildMenu(pos.x, pos.y)
      return
    }

    // 取消选择
    this.deselectTower()
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    // 由UI系统处理
  }
  
  /**
   * 选择塔
   */
  selectTower(tower) {
    // 取消之前的选择
    if (this.selectedTower) {
      this.selectedTower.isSelected = false
    }
    
    // 选择新塔
    this.selectedTower = tower
    tower.isSelected = true
    tower.rangeVisible = true
    
    // 显示塔信息UI
    this.battleUI.showTowerInfo(tower)
    
    // 退出建造模式
    this.exitBuildMode()
  }
  
  /**
   * 取消选择塔
   */
  deselectTower() {
    if (this.selectedTower) {
      this.selectedTower.isSelected = false
      this.selectedTower.rangeVisible = false
      this.selectedTower = null
    }
    
    // 隐藏塔信息UI
    this.battleUI.hideTowerInfo()
  }
  
  /**
   * 尝试建造塔
   */
  tryBuildTower(x, y) {
    if (!this.selectedTowerType) return
    
    // 获取建议的建造位置
    const buildPos = this.gameMap.getSuggestedBuildPosition(x, y)
    if (!buildPos) {
      wx.showToast({
        title: '此位置无法建造',
        icon: 'none'
      })
      return
    }
    
    // 检查金币是否足够
    const towerConfig = TowerConfig.getTowerLevelConfig(this.selectedTowerType, 1)
    if (!towerConfig || this.gold < towerConfig.cost) {
      wx.showToast({
        title: '金币不足',
        icon: 'none'
      })
      return
    }
    
    // 建造塔
    const tower = new Tower(buildPos.x, buildPos.y, this.selectedTowerType)
    tower.onAttack = this.combatSystem.towerAttack.bind(this.combatSystem)

    // 添加到实体管理器
    this.entityManager.addEntity(tower)

    // 占用位置
    this.gameMap.occupyPosition(buildPos.x, buildPos.y)

    // 扣除金币
    this.gold -= towerConfig.cost
    this.updateUIData()

    // 创建建造特效
    this.createEffect(buildPos.x, buildPos.y, 'level_up', { duration: 800 })

    // 播放建造音效
    this.playSfx('build_tower')

    // 记录统计数据
    DataManager.recordTowerBuilt()

    // 退出建造模式
    this.exitBuildMode()

    wx.showToast({
      title: '建造成功',
      icon: 'success'
    })
  }
  
  /**
   * 进入建造模式
   */
  enterBuildMode(towerType) {
    this.buildMode = true
    this.selectedTowerType = towerType
    this.deselectTower()
  }
  
  /**
   * 退出建造模式
   */
  exitBuildMode() {
    this.buildMode = false
    this.selectedTowerType = null
    this.battleUI.hideBuildMenu()
  }

  /**
   * 显示建造菜单
   */
  showBuildMenu(x, y) {
    // 保存建造位置
    this.buildPosition = { x, y }
    this.battleUI.showBuildMenu(x, y)
    this.deselectTower()
  }
  
  /**
   * 准备下一波敌人
   */
  prepareNextWave() {
    if (this.currentWave >= this.totalWaves) {
      // 所有波次完成
      this.checkVictory()
      return
    }
    
    const waveConfig = this.levelConfig.waves[this.currentWave]
    this.enemySpawnQueue = []
    
    // 为每种敌人类型创建生成队列
    waveConfig.enemies.forEach(enemyConfig => {
      for (let i = 0; i < enemyConfig.count; i++) {
        this.enemySpawnQueue.push({
          type: enemyConfig.type,
          spawnTime: Date.now() + waveConfig.delay + i * enemyConfig.interval,
          path: enemyConfig.path || 0,
          config: enemyConfig
        })
      }
    })
    
    // 按生成时间排序
    this.enemySpawnQueue.sort((a, b) => a.spawnTime - b.spawnTime)
    
    this.currentWave++
    this.updateUIData()
  }
  
  /**
   * 生成敌人
   */
  spawnEnemies() {
    const currentTime = Date.now()
    
    while (this.enemySpawnQueue.length > 0 && this.enemySpawnQueue[0].spawnTime <= currentTime) {
      const spawnData = this.enemySpawnQueue.shift()
      
      // 获取路径
      const path = this.gameMap.getPath(spawnData.path)
      if (!path) {
        console.error(`找不到路径: ${spawnData.path}`)
        continue
      }
      
      // 创建敌人
      const enemy = new Enemy(path, spawnData.type, this.getDifficultyMultiplier())
      enemy.onReachEnd = this.onEnemyReachEnd.bind(this)
      enemy.onDeath = this.onEnemyDeath.bind(this)
      
      // 添加到实体管理器
      this.entityManager.addEntity(enemy)
    }
    
    // 如果当前波次的敌人都生成完了，准备下一波
    if (this.enemySpawnQueue.length === 0 && this.entityManager.getEntityCount('enemy') === 0) {
      this.prepareNextWave()
    }
  }
  
  /**
   * 获取难度倍数
   */
  getDifficultyMultiplier() {
    // 根据关卡ID和波次计算难度
    return 1 + (this.levelId - 1) * 0.1 + (this.currentWave - 1) * 0.05
  }
  
  /**
   * 创建子弹
   */
  createBullet(tower, target) {
    const bulletConfig = {
      damage: tower.damage,
      speed: 300,
      bulletType: tower.bulletType,
      damageType: tower.damageType || 'physical',
      effects: this.getTowerEffects(tower),
      splashRadius: tower.splashRadius,
      splashDamage: tower.splashDamage
    }

    const bullet = new Bullet(tower.x, tower.y, target, bulletConfig)
    bullet.onHit = this.onBulletHit.bind(this)
    bullet.onCreateEffect = this.createEffect.bind(this)

    this.entityManager.addEntity(bullet)

    // 创建攻击特效
    this.createEffect(tower.x, tower.y, tower.attackEffect || 'muzzle_flash', { duration: 200 })

    // 播放攻击音效
    this.playSfx(`attack_${tower.bulletType}`, { volume: 0.6 })
  }

  /**
   * 子弹命中处理
   */
  onBulletHit(bullet, target) {
    // 调用战斗系统处理伤害
    this.combatSystem.bulletHit(bullet, target)

    // 创建命中特效
    this.createEffect(target.x, target.y, 'hit', { duration: 300 })

    // 播放命中音效
    this.playSfx('hit', { volume: 0.4 })
  }
  
  /**
   * 获取塔的效果
   */
  getTowerEffects(tower) {
    const effects = []
    
    if (tower.slowEffect > 0) {
      effects.push({
        type: 'slow',
        strength: tower.slowEffect,
        duration: tower.slowDuration
      })
    }
    
    if (tower.freezeChance > 0 && Math.random() < tower.freezeChance) {
      effects.push({
        type: 'freeze',
        duration: tower.freezeDuration
      })
    }
    
    if (tower.poisonDamage > 0) {
      effects.push({
        type: 'poison',
        damage: tower.poisonDamage,
        duration: tower.poisonDuration
      })
    }
    
    if (tower.armorReduction > 0) {
      effects.push({
        type: 'armor_reduction',
        amount: tower.armorReduction,
        duration: 5000
      })
    }
    
    return effects
  }
  
  /**
   * 敌人被击杀
   */
  onEnemyKilled(enemy) {
    // 已在CombatSystem中处理奖励
  }
  
  /**
   * 敌人到达终点
   */
  onEnemyReachEnd(enemy) {
    this.lives -= enemy.damage || 1
    this.updateUIData()

    // 创建生命值减少特效
    this.createEffect(enemy.x, enemy.y, 'hit', {
      duration: 500,
      color: '#ff0000',
      scale: 2
    })

    // 播放生命值减少音效
    this.playSfx('life_lost')

    if (this.lives <= 0) {
      this.gameState = 'defeat'
      this.onDefeat()
    }
  }

  /**
   * 敌人死亡
   */
  onEnemyDeath(enemy) {
    // 创建死亡特效
    this.createEffect(enemy.x, enemy.y, 'explosion', {
      duration: 600,
      scale: 1.5
    })

    // 创建金币拾取特效
    this.createEffect(enemy.x, enemy.y, 'coin_pickup', {
      duration: 800
    })

    // 播放死亡音效
    this.playSfx('enemy_death', { volume: 0.5 })

    // 记录击杀统计
    DataManager.recordEnemyKill()
  }
  
  /**
   * 获得奖励
   */
  onRewardGained(reward) {
    this.gold += reward.gold || 0
    this.score += reward.exp || 0
    this.updateUIData()
  }
  
  /**
   * 塔选择回调
   */
  onTowerSelect(towerData) {
    // 隐藏建造菜单
    this.battleUI.hideBuildMenu()

    // 如果有保存的建造位置，直接在该位置建造塔
    if (this.buildPosition) {
      this.selectedTowerType = towerData.id
      this.tryBuildTower(this.buildPosition.x, this.buildPosition.y)
      this.buildPosition = null
    }
  }
  
  /**
   * 塔升级回调
   */
  onTowerUpgrade(tower) {
    if (!tower) return
    
    const upgradeCost = TowerConfig.getUpgradeCost(tower.towerId, tower.level)
    if (upgradeCost === null) {
      wx.showToast({
        title: '已达到最高级',
        icon: 'none'
      })
      return
    }
    
    if (this.gold < upgradeCost) {
      wx.showToast({
        title: '金币不足',
        icon: 'none'
      })
      return
    }
    
    // 升级塔
    if (tower.upgrade()) {
      this.gold -= upgradeCost
      this.updateUIData()

      // 创建升级特效
      this.createEffect(tower.x, tower.y, 'level_up', {
        duration: 1000,
        scale: 2
      })

      // 播放升级音效
      this.playSfx('tower_upgrade')

      // 更新UI显示
      this.battleUI.showTowerInfo(tower)

      wx.showToast({
        title: '升级成功',
        icon: 'success'
      })
    }
  }
  
  /**
   * 塔出售回调
   */
  onTowerSell(tower) {
    if (!tower) return
    
    wx.showModal({
      title: '确认出售',
      content: `出售此塔可获得 ${tower.getSellValue()} 金币`,
      success: (res) => {
        if (res.confirm) {
          // 获得金币
          this.gold += tower.getSellValue()
          
          // 释放位置
          this.gameMap.releasePosition(tower.x, tower.y)
          
          // 移除塔
          this.entityManager.removeEntity(tower.id)
          
          // 取消选择
          this.deselectTower()
          
          this.updateUIData()
          
          wx.showToast({
            title: '出售成功',
            icon: 'success'
          })
        }
      }
    })
  }
  
  /**
   * 暂停切换回调
   */
  onPauseToggle(isPaused) {
    this.isPaused = isPaused
    this.gameState = isPaused ? 'paused' : 'playing'
  }
  
  /**
   * 速度改变回调
   */
  onSpeedChange(speed) {
    this.gameSpeed = speed
  }
  
  /**
   * 菜单显示回调
   */
  onMenuShow() {
    this.isPaused = true
    this.gameState = 'paused'
    
    // 显示暂停菜单
    wx.showActionSheet({
      itemList: ['继续游戏', '重新开始', '返回关卡选择'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.isPaused = false
            this.gameState = 'playing'
            break
          case 1:
            this.restartLevel()
            break
          case 2:
            this.exitToLevelSelect()
            break
        }
      }
    })
  }
  
  /**
   * 更新UI数据
   */
  updateUIData() {
    this.battleUI.updateGameData({
      gold: this.gold,
      lives: this.lives,
      wave: {
        current: this.currentWave,
        total: this.totalWaves
      },
      score: this.score
    })
  }
  
  /**
   * 检查胜利条件
   */
  checkVictory() {
    if (this.currentWave > this.totalWaves && this.entityManager.getEntityCount('enemy') === 0) {
      this.gameState = 'victory'
      this.onVictory()
    }
  }
  
  /**
   * 胜利处理
   */
  onVictory() {
    // 停止背景音乐
    AudioManager.stopMusic(1000)

    // 播放胜利音效
    this.playSfx('victory')

    // 计算游戏时间
    const gameTime = Date.now() - this.gameStartTime

    // 计算星级评价
    const stars = this.calculateStars()

    // 给予奖励
    const goldReward = this.levelConfig.rewards.gold
    const expReward = this.levelConfig.rewards.exp

    DataManager.addGold(goldReward)
    DataManager.addExp(expReward)
    DataManager.completeLevel(this.levelId, stars, gameTime)
    DataManager.recordPlayTime(gameTime)

    // 更新玩家金币
    this.gold += goldReward

    wx.showModal({
      title: '胜利！',
      content: `恭喜通过关卡 ${this.levelId}！\n⭐ 星级评价: ${stars}/3\n💰 获得金币: ${goldReward}\n✨ 获得经验: ${expReward}\n⏱️ 用时: ${Math.floor(gameTime/1000)}秒`,
      showCancel: false,
      success: () => {
        this.exitToLevelSelect()
      }
    })
  }

  /**
   * 失败处理
   */
  onDefeat() {
    // 停止背景音乐
    AudioManager.stopMusic(500)

    // 播放失败音效
    this.playSfx('defeat')

    // 记录游戏时间
    const gameTime = Date.now() - this.gameStartTime
    DataManager.recordPlayTime(gameTime)

    wx.showModal({
      title: '失败',
      content: '生命值耗尽，挑战失败！',
      confirmText: '重新挑战',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          this.restartLevel()
        } else {
          this.exitToLevelSelect()
        }
      }
    })
  }

  /**
   * 计算星级评价
   */
  calculateStars() {
    const livesPercent = this.lives / this.levelConfig.initialResources.lives

    if (livesPercent >= 0.9) return 3  // 90%以上生命值
    if (livesPercent >= 0.7) return 2  // 70%以上生命值
    return 1  // 完成关卡
  }

  /**
   * 性能警告处理
   */
  onPerformanceWarning(warnings) {
    // 只在调试模式下显示性能警告
    if (this.showDebugInfo || DataManager.getSetting('showFPS')) {
      warnings.forEach(warning => {
        console.warn(`性能警告: ${warning.message}`)
      })
    }

    // 自动优化建议
    const fpsWarning = warnings.find(w => w.type === 'low_fps')
    if (fpsWarning && fpsWarning.value < 20) {
      // FPS过低时自动降低游戏速度
      if (this.gameSpeed > 0.5) {
        this.gameSpeed = Math.max(0.5, this.gameSpeed - 0.1)
        console.log(`自动降低游戏速度至: ${this.gameSpeed}`)
      }
    }

    const memoryWarning = warnings.find(w => w.type === 'high_memory')
    if (memoryWarning && memoryWarning.value > 0.9) {
      // 内存过高时强制垃圾回收
      if (typeof wx !== 'undefined' && wx.triggerGC) {
        wx.triggerGC()
        console.log('触发垃圾回收')
      }
    }
  }
  
  /**
   * 重新开始关卡
   */
  restartLevel() {
    // 清理当前状态
    this.cleanup()
    
    // 重新初始化
    this.initializeGame()
  }
  
  /**
   * 退出到关卡选择
   */
  exitToLevelSelect() {
    if (this.onSceneChange) {
      this.onSceneChange('adventure-map')
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    // 开始更新计时
    PerformanceMonitor.startUpdateTiming()

    if (this.isPaused || this.gameState !== 'playing') {
      PerformanceMonitor.endUpdateTiming()
      return
    }

    // 应用游戏速度
    const adjustedDeltaTime = deltaTime * this.gameSpeed

    // 更新核心系统
    this.entityManager.update(adjustedDeltaTime)
    this.effectManager.update(adjustedDeltaTime)
    this.battleUI.update(adjustedDeltaTime)

    // 塔的目标选择和攻击
    this.updateTowers(adjustedDeltaTime)

    // 生成敌人
    this.spawnEnemies()

    // 检查胜利条件
    this.checkVictory()

    // 更新性能统计
    this.updatePerformanceStats()

    // 结束更新计时
    PerformanceMonitor.endUpdateTiming()
  }

  /**
   * 更新塔的行为
   */
  updateTowers(deltaTime) {
    const enemies = this.entityManager.getActiveEnemies()

    for (const tower of this.entityManager.getTowers()) {
      if (!tower.isAlive || tower.isBuilding) continue

      // 选择目标
      tower.selectTarget(enemies)

      // 攻击目标
      if (tower.target) {
        tower.attack()
      }
    }
  }

  /**
   * 更新性能统计
   */
  updatePerformanceStats() {
    this.frameCount++
    const currentTime = Date.now()

    if (currentTime - this.lastFrameTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastFrameTime = currentTime

      // 性能警告
      if (this.fps < 30) {
        console.warn(`性能警告: FPS = ${this.fps}`)
      }
    }
  }

  /**
   * 渲染场景
   */
  render() {
    // 开始渲染计时
    PerformanceMonitor.startRenderTiming()

    // 更新FPS
    PerformanceMonitor.updateFPS()

    // 清空画布
    this.ctx.clearRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())

    // 渲染地图
    this.gameMap.render(this.ctx)

    // 渲染建造预览
    if (this.buildMode && this.selectedTowerType) {
      this.renderBuildPreview()
    }

    // 渲染所有实体
    this.entityManager.render(this.ctx)

    // 渲染UI
    this.battleUI.render()

    // 渲染调试信息
    if (this.showDebugInfo || DataManager.getSetting('showFPS')) {
      this.renderDebugInfo()
    }

    // 结束渲染计时
    PerformanceMonitor.endRenderTiming()
  }

  /**
   * 渲染建造预览
   */
  renderBuildPreview() {
    // 这里可以添加建造预览的渲染逻辑
    // 比如显示塔的攻击范围等
  }

  /**
   * 渲染调试信息
   */
  renderDebugInfo() {
    const stats = this.entityManager.getStats()
    const memoryUsage = this.entityManager.getMemoryUsage()
    const perfReport = PerformanceMonitor.getPerformanceReport()

    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    this.ctx.fillRect(10, this.adapter.getWindowHeight() - 160, 250, 150)

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '11px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'top'

    const debugInfo = [
      `FPS: ${perfReport.fps.current} (avg: ${perfReport.fps.average})`,
      `Render: ${perfReport.timing.render}ms`,
      `Update: ${perfReport.timing.update}ms`,
      `Memory: ${perfReport.memory.percentage}%`,
      `Entities: ${stats.totalEntities}`,
      `Towers: ${stats.activeTowers}`,
      `Enemies: ${stats.activeEnemies}`,
      `Bullets: ${stats.activeBullets}`,
      `Effects: ${stats.activeEffects}`,
      `Pool: ${memoryUsage.totalMemoryObjects}`,
      `Wave: ${this.currentWave}/${this.totalWaves}`,
      `Gold: ${this.gold} | Lives: ${this.lives}`
    ]

    debugInfo.forEach((info, index) => {
      // 根据性能状态设置颜色
      let color = '#ffffff'
      if (info.includes('FPS:') && perfReport.fps.current < 30) color = '#ff6666'
      if (info.includes('Memory:') && perfReport.memory.percentage > 70) color = '#ffaa66'
      if (info.includes('Render:') && perfReport.timing.render > 16) color = '#ffaa66'

      this.ctx.fillStyle = color
      this.ctx.fillText(info, 15, this.adapter.getWindowHeight() - 150 + index * 12)
    })
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 停止音乐
    AudioManager.stopMusic()

    // 停止所有音效
    AudioManager.stopAllSfx()

    // 记录游戏时间
    if (this.gameStartTime) {
      const gameTime = Date.now() - this.gameStartTime
      DataManager.recordPlayTime(gameTime)
    }

    // 清理事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    // 清理实体
    this.entityManager.clear()

    // 清理UI
    if (this.battleUI && this.battleUI.destroy) {
      this.battleUI.destroy()
    }

    // 重置状态
    this.gameState = 'preparing'
    this.currentWave = 0
    this.enemySpawnQueue = []
    this.selectedTower = null
    this.buildMode = false
    this.selectedTowerType = null

    this.touchStartHandler = null
    this.touchEndHandler = null
  }

  /**
   * 销毁场景
   */
  destroy() {
    this.cleanup()
  }

  /**
   * 获取场景信息
   */
  getSceneInfo() {
    return {
      levelId: this.levelId,
      gameState: this.gameState,
      currentWave: this.currentWave,
      totalWaves: this.totalWaves,
      gold: this.gold,
      lives: this.lives,
      score: this.score,
      fps: this.fps,
      entityStats: this.entityManager.getStats()
    }
  }

  /**
   * 切换调试信息显示
   */
  toggleDebugInfo() {
    this.showDebugInfo = !this.showDebugInfo
  }

  /**
   * 切换地图网格显示
   */
  toggleMapGrid() {
    this.gameMap.showGrid = !this.gameMap.showGrid
  }

  /**
   * 切换建造区域显示
   */
  toggleBuildableAreas() {
    this.gameMap.showBuildableAreas = !this.gameMap.showBuildableAreas
  }
}

// 导出模块
module.exports = BattleScene
