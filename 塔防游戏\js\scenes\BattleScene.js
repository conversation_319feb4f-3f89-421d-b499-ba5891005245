/**
 * 战斗场景
 * 塔防游戏的核心战斗场景
 */
const ScreenAdapter = require('../utils/ScreenAdapter.js')
const EntityManager = require('../systems/EntityManager.js')
const GameMap = require('../systems/GameMap.js')
const { CombatSystem, EffectManager } = require('../systems/CombatSystem.js')
const BattleUI = require('../ui/BattleUI.js')

// 实体类
const Tower = require('../entities/Tower.js')
const Enemy = require('../entities/Enemy.js')
const Bullet = require('../entities/Bullet.js')

// 配置
const LevelConfig = require('../config/LevelConfig.js')
const TowerConfig = require('../config/TowerConfig.js')
const EnemyConfig = require('../config/EnemyConfig.js')

class BattleScene {
  constructor(canvas, ctx, levelId = 1) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 关卡配置
    this.levelId = levelId
    this.levelConfig = LevelConfig.getLevelConfig(levelId)
    
    if (!this.levelConfig) {
      console.error(`找不到关卡配置: ${levelId}`)
      return
    }
    
    // 核心系统
    this.entityManager = new EntityManager()
    this.gameMap = new GameMap(this.levelConfig.map)
    this.combatSystem = new CombatSystem()
    this.effectManager = new EffectManager()
    this.battleUI = new BattleUI(canvas, ctx)
    
    // 连接系统
    this.combatSystem.setEffectManager(this.effectManager)
    this.setupSystemCallbacks()
    
    // 游戏状态
    this.gameState = 'preparing'  // preparing, playing, paused, victory, defeat
    this.gameSpeed = 1
    this.isPaused = false
    
    // 资源
    this.gold = this.levelConfig.initialResources.gold
    this.lives = this.levelConfig.initialResources.lives
    this.score = 0
    
    // 波次管理
    this.currentWave = 0
    this.totalWaves = this.levelConfig.waves.length
    this.waveStartTime = 0
    this.enemySpawnQueue = []
    this.lastEnemySpawnTime = 0
    
    // 选中状态
    this.selectedTower = null
    this.buildMode = false
    this.selectedTowerType = null
    
    // 性能监控
    this.lastFrameTime = 0
    this.fps = 60
    this.frameCount = 0
    
    // 绑定事件
    this.bindEvents()
    
    // 初始化游戏
    this.initializeGame()
  }
  
  /**
   * 设置系统回调
   */
  setupSystemCallbacks() {
    // 战斗系统回调
    this.combatSystem.onBulletCreate = this.createBullet.bind(this)
    this.combatSystem.onEnemyKilled = this.onEnemyKilled.bind(this)
    this.combatSystem.onRewardGained = this.onRewardGained.bind(this)
    this.combatSystem.getEnemiesInRadius = this.entityManager.getEnemiesInRadius.bind(this.entityManager)
    
    // UI回调
    this.battleUI.onTowerSelect = this.onTowerSelect.bind(this)
    this.battleUI.onTowerUpgrade = this.onTowerUpgrade.bind(this)
    this.battleUI.onTowerSell = this.onTowerSell.bind(this)
    this.battleUI.onPauseToggle = this.onPauseToggle.bind(this)
    this.battleUI.onSpeedChange = this.onSpeedChange.bind(this)
    this.battleUI.onMenuShow = this.onMenuShow.bind(this)
  }
  
  /**
   * 初始化游戏
   */
  initializeGame() {
    // 更新UI数据
    this.updateUIData()
    
    // 准备第一波敌人
    this.prepareNextWave()
    
    // 开始游戏
    this.gameState = 'playing'
  }
  
  /**
   * 绑定触摸事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    
    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    if (this.isPaused) return
    
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }
    
    // 检查是否点击了塔
    const tower = this.entityManager.getTowerAt(pos.x, pos.y)
    if (tower) {
      this.selectTower(tower)
      return
    }
    
    // 检查是否可以建造
    if (this.buildMode && this.selectedTowerType) {
      this.tryBuildTower(pos.x, pos.y)
      return
    }
    
    // 取消选择
    this.deselectTower()
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    // 由UI系统处理
  }
  
  /**
   * 选择塔
   */
  selectTower(tower) {
    // 取消之前的选择
    if (this.selectedTower) {
      this.selectedTower.isSelected = false
    }
    
    // 选择新塔
    this.selectedTower = tower
    tower.isSelected = true
    tower.rangeVisible = true
    
    // 显示塔信息UI
    this.battleUI.showTowerInfo(tower)
    
    // 退出建造模式
    this.exitBuildMode()
  }
  
  /**
   * 取消选择塔
   */
  deselectTower() {
    if (this.selectedTower) {
      this.selectedTower.isSelected = false
      this.selectedTower.rangeVisible = false
      this.selectedTower = null
    }
    
    // 隐藏塔信息UI
    this.battleUI.hideTowerInfo()
  }
  
  /**
   * 尝试建造塔
   */
  tryBuildTower(x, y) {
    if (!this.selectedTowerType) return
    
    // 获取建议的建造位置
    const buildPos = this.gameMap.getSuggestedBuildPosition(x, y)
    if (!buildPos) {
      wx.showToast({
        title: '此位置无法建造',
        icon: 'none'
      })
      return
    }
    
    // 检查金币是否足够
    const towerConfig = TowerConfig.getTowerLevelConfig(this.selectedTowerType, 1)
    if (!towerConfig || this.gold < towerConfig.cost) {
      wx.showToast({
        title: '金币不足',
        icon: 'none'
      })
      return
    }
    
    // 建造塔
    const tower = new Tower(buildPos.x, buildPos.y, this.selectedTowerType)
    tower.onAttack = this.combatSystem.towerAttack.bind(this.combatSystem)
    
    // 添加到实体管理器
    this.entityManager.addEntity(tower)
    
    // 占用位置
    this.gameMap.occupyPosition(buildPos.x, buildPos.y)
    
    // 扣除金币
    this.gold -= towerConfig.cost
    this.updateUIData()
    
    // 退出建造模式
    this.exitBuildMode()
    
    wx.showToast({
      title: '建造成功',
      icon: 'success'
    })
  }
  
  /**
   * 进入建造模式
   */
  enterBuildMode(towerType) {
    this.buildMode = true
    this.selectedTowerType = towerType
    this.deselectTower()
  }
  
  /**
   * 退出建造模式
   */
  exitBuildMode() {
    this.buildMode = false
    this.selectedTowerType = null
    this.battleUI.hideBuildMenu()
  }
  
  /**
   * 准备下一波敌人
   */
  prepareNextWave() {
    if (this.currentWave >= this.totalWaves) {
      // 所有波次完成
      this.checkVictory()
      return
    }
    
    const waveConfig = this.levelConfig.waves[this.currentWave]
    this.enemySpawnQueue = []
    
    // 为每种敌人类型创建生成队列
    waveConfig.enemies.forEach(enemyConfig => {
      for (let i = 0; i < enemyConfig.count; i++) {
        this.enemySpawnQueue.push({
          type: enemyConfig.type,
          spawnTime: Date.now() + waveConfig.delay + i * enemyConfig.interval,
          path: enemyConfig.path || 0,
          config: enemyConfig
        })
      }
    })
    
    // 按生成时间排序
    this.enemySpawnQueue.sort((a, b) => a.spawnTime - b.spawnTime)
    
    this.currentWave++
    this.updateUIData()
  }
  
  /**
   * 生成敌人
   */
  spawnEnemies() {
    const currentTime = Date.now()
    
    while (this.enemySpawnQueue.length > 0 && this.enemySpawnQueue[0].spawnTime <= currentTime) {
      const spawnData = this.enemySpawnQueue.shift()
      
      // 获取路径
      const path = this.gameMap.getPath(spawnData.path)
      if (!path) {
        console.error(`找不到路径: ${spawnData.path}`)
        continue
      }
      
      // 创建敌人
      const enemy = new Enemy(path, spawnData.type, this.getDifficultyMultiplier())
      enemy.onReachEnd = this.onEnemyReachEnd.bind(this)
      enemy.onDeath = this.onEnemyDeath.bind(this)
      
      // 添加到实体管理器
      this.entityManager.addEntity(enemy)
    }
    
    // 如果当前波次的敌人都生成完了，准备下一波
    if (this.enemySpawnQueue.length === 0 && this.entityManager.getEntityCount('enemy') === 0) {
      this.prepareNextWave()
    }
  }
  
  /**
   * 获取难度倍数
   */
  getDifficultyMultiplier() {
    // 根据关卡ID和波次计算难度
    return 1 + (this.levelId - 1) * 0.1 + (this.currentWave - 1) * 0.05
  }
  
  /**
   * 创建子弹
   */
  createBullet(tower, target) {
    const bulletConfig = {
      damage: tower.damage,
      speed: 300,
      bulletType: tower.bulletType,
      damageType: tower.damageType || 'physical',
      effects: this.getTowerEffects(tower),
      splashRadius: tower.splashRadius,
      splashDamage: tower.splashDamage
    }
    
    const bullet = new Bullet(tower.x, tower.y, target, bulletConfig)
    bullet.onHit = this.combatSystem.bulletHit.bind(this.combatSystem)
    
    this.entityManager.addEntity(bullet)
  }
  
  /**
   * 获取塔的效果
   */
  getTowerEffects(tower) {
    const effects = []
    
    if (tower.slowEffect > 0) {
      effects.push({
        type: 'slow',
        strength: tower.slowEffect,
        duration: tower.slowDuration
      })
    }
    
    if (tower.freezeChance > 0 && Math.random() < tower.freezeChance) {
      effects.push({
        type: 'freeze',
        duration: tower.freezeDuration
      })
    }
    
    if (tower.poisonDamage > 0) {
      effects.push({
        type: 'poison',
        damage: tower.poisonDamage,
        duration: tower.poisonDuration
      })
    }
    
    if (tower.armorReduction > 0) {
      effects.push({
        type: 'armor_reduction',
        amount: tower.armorReduction,
        duration: 5000
      })
    }
    
    return effects
  }
  
  /**
   * 敌人被击杀
   */
  onEnemyKilled(enemy) {
    // 已在CombatSystem中处理奖励
  }
  
  /**
   * 敌人到达终点
   */
  onEnemyReachEnd(enemy) {
    this.lives -= enemy.damage || 1
    this.updateUIData()
    
    if (this.lives <= 0) {
      this.gameState = 'defeat'
      this.onDefeat()
    }
  }
  
  /**
   * 敌人死亡
   */
  onEnemyDeath(enemy) {
    // 可以在这里添加死亡特效
  }
  
  /**
   * 获得奖励
   */
  onRewardGained(reward) {
    this.gold += reward.gold || 0
    this.score += reward.exp || 0
    this.updateUIData()
  }
  
  /**
   * 塔选择回调
   */
  onTowerSelect(towerData) {
    this.enterBuildMode(towerData.id)
    this.battleUI.hideBuildMenu()
  }
  
  /**
   * 塔升级回调
   */
  onTowerUpgrade(tower) {
    if (!tower) return
    
    const upgradeCost = TowerConfig.getUpgradeCost(tower.towerId, tower.level)
    if (upgradeCost === null) {
      wx.showToast({
        title: '已达到最高级',
        icon: 'none'
      })
      return
    }
    
    if (this.gold < upgradeCost) {
      wx.showToast({
        title: '金币不足',
        icon: 'none'
      })
      return
    }
    
    // 升级塔
    if (tower.upgrade()) {
      this.gold -= upgradeCost
      this.updateUIData()
      
      // 更新UI显示
      this.battleUI.showTowerInfo(tower)
      
      wx.showToast({
        title: '升级成功',
        icon: 'success'
      })
    }
  }
  
  /**
   * 塔出售回调
   */
  onTowerSell(tower) {
    if (!tower) return
    
    wx.showModal({
      title: '确认出售',
      content: `出售此塔可获得 ${tower.getSellValue()} 金币`,
      success: (res) => {
        if (res.confirm) {
          // 获得金币
          this.gold += tower.getSellValue()
          
          // 释放位置
          this.gameMap.releasePosition(tower.x, tower.y)
          
          // 移除塔
          this.entityManager.removeEntity(tower.id)
          
          // 取消选择
          this.deselectTower()
          
          this.updateUIData()
          
          wx.showToast({
            title: '出售成功',
            icon: 'success'
          })
        }
      }
    })
  }
  
  /**
   * 暂停切换回调
   */
  onPauseToggle(isPaused) {
    this.isPaused = isPaused
    this.gameState = isPaused ? 'paused' : 'playing'
  }
  
  /**
   * 速度改变回调
   */
  onSpeedChange(speed) {
    this.gameSpeed = speed
  }
  
  /**
   * 菜单显示回调
   */
  onMenuShow() {
    this.isPaused = true
    this.gameState = 'paused'
    
    // 显示暂停菜单
    wx.showActionSheet({
      itemList: ['继续游戏', '重新开始', '返回关卡选择'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.isPaused = false
            this.gameState = 'playing'
            break
          case 1:
            this.restartLevel()
            break
          case 2:
            this.exitToLevelSelect()
            break
        }
      }
    })
  }
  
  /**
   * 更新UI数据
   */
  updateUIData() {
    this.battleUI.updateGameData({
      gold: this.gold,
      lives: this.lives,
      wave: {
        current: this.currentWave,
        total: this.totalWaves
      },
      score: this.score
    })
  }
  
  /**
   * 检查胜利条件
   */
  checkVictory() {
    if (this.currentWave > this.totalWaves && this.entityManager.getEntityCount('enemy') === 0) {
      this.gameState = 'victory'
      this.onVictory()
    }
  }
  
  /**
   * 胜利处理
   */
  onVictory() {
    wx.showModal({
      title: '胜利！',
      content: `恭喜通过关卡 ${this.levelId}！\n获得金币: ${this.levelConfig.rewards.gold}\n获得经验: ${this.levelConfig.rewards.exp}`,
      showCancel: false,
      success: () => {
        this.exitToLevelSelect()
      }
    })
  }
  
  /**
   * 失败处理
   */
  onDefeat() {
    wx.showModal({
      title: '失败',
      content: '生命值耗尽，挑战失败！',
      confirmText: '重新挑战',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          this.restartLevel()
        } else {
          this.exitToLevelSelect()
        }
      }
    })
  }
  
  /**
   * 重新开始关卡
   */
  restartLevel() {
    // 清理当前状态
    this.cleanup()
    
    // 重新初始化
    this.initializeGame()
  }
  
  /**
   * 退出到关卡选择
   */
  exitToLevelSelect() {
    if (this.onSceneChange) {
      this.onSceneChange('adventure-map')
    }
  }
  
  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }
  
  /**
   * 更新场景
   */
  update(deltaTime) {
    if (this.isPaused || this.gameState !== 'playing') {
      return
    }
    
    // 应用游戏速度
    const adjustedDeltaTime = deltaTime * this.gameSpeed
    
    // 更新核心系统
    this.entityManager.update(adjustedDeltaTime)
    this.effectManager.update(adjustedDeltaTime)
    
    // 塔的目标选择和攻击
    this.updateTowers(adjustedDeltaTime)
    
    // 生成敌人
    this.spawnEnemies()
    
    // 检查胜利条件
    this.checkVictory()
    
    // 更新性能统计
    this.updatePerformanceStats()
  }

  /**
   * 更新塔的行为
   */
  updateTowers(deltaTime) {
    const enemies = this.entityManager.getActiveEnemies()

    for (const tower of this.entityManager.getTowers()) {
      if (!tower.isAlive || tower.isBuilding) continue

      // 选择目标
      tower.selectTarget(enemies)

      // 攻击目标
      if (tower.target) {
        tower.attack()
      }
    }
  }

  /**
   * 更新性能统计
   */
  updatePerformanceStats() {
    this.frameCount++
    const currentTime = Date.now()

    if (currentTime - this.lastFrameTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastFrameTime = currentTime

      // 性能警告
      if (this.fps < 30) {
        console.warn(`性能警告: FPS = ${this.fps}`)
      }
    }
  }

  /**
   * 渲染场景
   */
  render() {
    // 清空画布
    this.ctx.clearRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())

    // 应用像素比缩放
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 渲染地图
    this.gameMap.render(this.ctx)

    // 渲染建造预览
    if (this.buildMode && this.selectedTowerType) {
      this.renderBuildPreview()
    }

    // 渲染所有实体
    this.entityManager.render(this.ctx)

    // 渲染UI
    this.battleUI.render()

    // 渲染调试信息
    if (this.showDebugInfo) {
      this.renderDebugInfo()
    }

    this.ctx.restore()
  }

  /**
   * 渲染建造预览
   */
  renderBuildPreview() {
    // 这里可以添加建造预览的渲染逻辑
    // 比如显示塔的攻击范围等
  }

  /**
   * 渲染调试信息
   */
  renderDebugInfo() {
    const stats = this.entityManager.getStats()
    const memoryUsage = this.entityManager.getMemoryUsage()

    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(10, this.adapter.getWindowHeight() - 120, 200, 110)

    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'top'

    const debugInfo = [
      `FPS: ${this.fps}`,
      `Entities: ${stats.totalEntities}`,
      `Towers: ${stats.activeTowers}`,
      `Enemies: ${stats.activeEnemies}`,
      `Bullets: ${stats.activeBullets}`,
      `Memory: ${memoryUsage.totalMemoryObjects}`,
      `Wave: ${this.currentWave}/${this.totalWaves}`,
      `Gold: ${this.gold}`,
      `Lives: ${this.lives}`
    ]

    debugInfo.forEach((info, index) => {
      this.ctx.fillText(info, 15, this.adapter.getWindowHeight() - 110 + index * 12)
    })
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 清理事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    // 清理实体
    this.entityManager.clear()

    // 清理UI
    if (this.battleUI && this.battleUI.destroy) {
      this.battleUI.destroy()
    }

    // 重置状态
    this.gameState = 'preparing'
    this.currentWave = 0
    this.enemySpawnQueue = []
    this.selectedTower = null
    this.buildMode = false
    this.selectedTowerType = null

    this.touchStartHandler = null
    this.touchEndHandler = null
  }

  /**
   * 销毁场景
   */
  destroy() {
    this.cleanup()
  }

  /**
   * 获取场景信息
   */
  getSceneInfo() {
    return {
      levelId: this.levelId,
      gameState: this.gameState,
      currentWave: this.currentWave,
      totalWaves: this.totalWaves,
      gold: this.gold,
      lives: this.lives,
      score: this.score,
      fps: this.fps,
      entityStats: this.entityManager.getStats()
    }
  }

  /**
   * 切换调试信息显示
   */
  toggleDebugInfo() {
    this.showDebugInfo = !this.showDebugInfo
  }

  /**
   * 切换地图网格显示
   */
  toggleMapGrid() {
    this.gameMap.showGrid = !this.gameMap.showGrid
  }

  /**
   * 切换建造区域显示
   */
  toggleBuildableAreas() {
    this.gameMap.showBuildableAreas = !this.gameMap.showBuildableAreas
  }
}

// 导出模块
module.exports = BattleScene
