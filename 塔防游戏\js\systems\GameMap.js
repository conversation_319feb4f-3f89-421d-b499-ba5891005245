/**
 * 游戏地图系统
 * 处理地图、路径和建造区域
 */

class GameMap {
  /**
   * 构造函数
   * @param {Object} mapConfig - 地图配置
   */
  constructor(mapConfig) {
    // 基本属性
    this.width = mapConfig.width || 800
    this.height = mapConfig.height || 600
    this.gridSize = mapConfig.gridSize || 40
    this.background = mapConfig.background || 'grass'
    
    // 计算网格数量
    this.gridCols = Math.ceil(this.width / this.gridSize)
    this.gridRows = Math.ceil(this.height / this.gridSize)
    
    // 初始化网格
    this.grid = this.createGrid()
    
    // 路径系统
    this.paths = []
    this.spawnPoints = []
    this.endPoints = []
    
    // 建造区域
    this.buildableAreas = []
    
    // 加载地图配置
    this.loadMapConfig(mapConfig)
    
    // 视觉相关
    this.showGrid = false
    this.showPaths = true
    this.showBuildableAreas = false
  }
  
  /**
   * 创建网格
   */
  createGrid() {
    const grid = []
    for (let row = 0; row < this.gridRows; row++) {
      grid[row] = []
      for (let col = 0; col < this.gridCols; col++) {
        grid[row][col] = {
          type: 'empty',     // empty, path, obstacle, buildable
          x: col * this.gridSize,
          y: row * this.gridSize,
          col: col,
          row: row,
          occupied: false
        }
      }
    }
    return grid
  }
  
  /**
   * 加载地图配置
   */
  loadMapConfig(config) {
    // 加载路径
    if (config.path) {
      this.addPath(config.path)
    }
    
    if (config.paths) {
      config.paths.forEach(path => this.addPath(path))
    }
    
    // 加载建造区域
    if (config.buildableAreas) {
      this.buildableAreas = config.buildableAreas
      this.markBuildableAreas()
    }
    
    // 加载障碍物
    if (config.obstacles) {
      this.addObstacles(config.obstacles)
    }
  }
  
  /**
   * 添加路径
   * @param {Array} pathPoints - 路径点数组（相对坐标 0-1）
   */
  addPath(pathPoints) {
    if (!pathPoints || pathPoints.length < 2) {
      console.error('路径至少需要2个点')
      return
    }
    
    // 转换相对坐标为绝对坐标
    const absolutePath = pathPoints.map(point => ({
      x: point.x * this.width,
      y: point.y * this.height
    }))
    
    this.paths.push(absolutePath)
    
    // 记录起点和终点
    this.spawnPoints.push(absolutePath[0])
    this.endPoints.push(absolutePath[absolutePath.length - 1])
    
    // 在网格中标记路径
    this.markPathInGrid(absolutePath)
  }
  
  /**
   * 在网格中标记路径
   */
  markPathInGrid(path) {
    for (let i = 0; i < path.length - 1; i++) {
      const start = path[i]
      const end = path[i + 1]
      
      // 在起点和终点之间插值，标记路径格子
      const distance = Math.sqrt((end.x - start.x) ** 2 + (end.y - start.y) ** 2)
      const steps = Math.ceil(distance / (this.gridSize / 2))
      
      for (let step = 0; step <= steps; step++) {
        const t = step / steps
        const x = start.x + (end.x - start.x) * t
        const y = start.y + (end.y - start.y) * t
        
        const gridPos = this.worldToGrid(x, y)
        if (this.isValidGridPosition(gridPos.col, gridPos.row)) {
          this.grid[gridPos.row][gridPos.col].type = 'path'
        }
      }
    }
  }
  
  /**
   * 标记建造区域
   */
  markBuildableAreas() {
    this.buildableAreas.forEach(area => {
      for (let row = area.y; row < area.y + area.height; row++) {
        for (let col = area.x; col < area.x + area.width; col++) {
          if (this.isValidGridPosition(col, row)) {
            // 只有空地才能标记为可建造
            if (this.grid[row][col].type === 'empty') {
              this.grid[row][col].type = 'buildable'
            }
          }
        }
      }
    })
  }
  
  /**
   * 添加障碍物
   */
  addObstacles(obstacles) {
    obstacles.forEach(obstacle => {
      const gridPos = this.worldToGrid(obstacle.x, obstacle.y)
      if (this.isValidGridPosition(gridPos.col, gridPos.row)) {
        this.grid[gridPos.row][gridPos.col].type = 'obstacle'
      }
    })
  }
  
  /**
   * 世界坐标转网格坐标
   */
  worldToGrid(x, y) {
    return {
      col: Math.floor(x / this.gridSize),
      row: Math.floor(y / this.gridSize)
    }
  }
  
  /**
   * 网格坐标转世界坐标
   */
  gridToWorld(col, row) {
    return {
      x: col * this.gridSize + this.gridSize / 2,
      y: row * this.gridSize + this.gridSize / 2
    }
  }
  
  /**
   * 检查网格位置是否有效
   */
  isValidGridPosition(col, row) {
    return col >= 0 && col < this.gridCols && row >= 0 && row < this.gridRows
  }
  
  /**
   * 检查位置是否可以建造
   * @param {number} x - 世界x坐标
   * @param {number} y - 世界y坐标
   */
  canBuildAt(x, y) {
    const gridPos = this.worldToGrid(x, y)
    
    if (!this.isValidGridPosition(gridPos.col, gridPos.row)) {
      return false
    }
    
    const cell = this.grid[gridPos.row][gridPos.col]
    
    // 检查格子类型和占用状态
    return (cell.type === 'buildable' || cell.type === 'empty') && !cell.occupied
  }
  
  /**
   * 占用位置
   * @param {number} x - 世界x坐标
   * @param {number} y - 世界y坐标
   */
  occupyPosition(x, y) {
    const gridPos = this.worldToGrid(x, y)
    
    if (this.isValidGridPosition(gridPos.col, gridPos.row)) {
      this.grid[gridPos.row][gridPos.col].occupied = true
      return true
    }
    
    return false
  }
  
  /**
   * 释放位置
   * @param {number} x - 世界x坐标
   * @param {number} y - 世界y坐标
   */
  releasePosition(x, y) {
    const gridPos = this.worldToGrid(x, y)
    
    if (this.isValidGridPosition(gridPos.col, gridPos.row)) {
      this.grid[gridPos.row][gridPos.col].occupied = false
      return true
    }
    
    return false
  }
  
  /**
   * 获取路径
   * @param {number} pathIndex - 路径索引
   */
  getPath(pathIndex = 0) {
    return this.paths[pathIndex] || null
  }
  
  /**
   * 获取所有路径
   */
  getAllPaths() {
    return this.paths
  }
  
  /**
   * 获取生成点
   * @param {number} pathIndex - 路径索引
   */
  getSpawnPoint(pathIndex = 0) {
    return this.spawnPoints[pathIndex] || null
  }
  
  /**
   * 获取终点
   * @param {number} pathIndex - 路径索引
   */
  getEndPoint(pathIndex = 0) {
    return this.endPoints[pathIndex] || null
  }
  
  /**
   * 获取建议的建造位置
   * @param {number} x - 点击的x坐标
   * @param {number} y - 点击的y坐标
   */
  getSuggestedBuildPosition(x, y) {
    const gridPos = this.worldToGrid(x, y)
    
    // 如果点击位置可以建造，直接返回网格中心
    if (this.canBuildAt(x, y)) {
      return this.gridToWorld(gridPos.col, gridPos.row)
    }
    
    // 否则寻找附近可建造的位置
    const searchRadius = 2
    for (let radius = 1; radius <= searchRadius; radius++) {
      for (let dx = -radius; dx <= radius; dx++) {
        for (let dy = -radius; dy <= radius; dy++) {
          const checkCol = gridPos.col + dx
          const checkRow = gridPos.row + dy
          
          if (this.isValidGridPosition(checkCol, checkRow)) {
            const worldPos = this.gridToWorld(checkCol, checkRow)
            if (this.canBuildAt(worldPos.x, worldPos.y)) {
              return worldPos
            }
          }
        }
      }
    }
    
    return null
  }
  
  /**
   * 渲染地图
   * @param {CanvasRenderingContext2D} ctx - 渲染上下文
   */
  render(ctx) {
    // 绘制背景
    this.drawBackground(ctx)
    
    // 绘制网格（如果启用）
    if (this.showGrid) {
      this.drawGrid(ctx)
    }
    
    // 绘制建造区域（如果启用）
    if (this.showBuildableAreas) {
      this.drawBuildableAreas(ctx)
    }
    
    // 绘制路径
    if (this.showPaths) {
      this.drawPaths(ctx)
    }
    
    // 绘制生成点和终点
    this.drawSpawnAndEndPoints(ctx)
  }
  
  /**
   * 绘制背景
   */
  drawBackground(ctx) {
    // 根据背景类型绘制不同的背景
    switch (this.background) {
      case 'grass':
        ctx.fillStyle = '#90EE90'
        break
      case 'forest':
        ctx.fillStyle = '#228B22'
        break
      case 'desert':
        ctx.fillStyle = '#F4A460'
        break
      case 'snow':
        ctx.fillStyle = '#F0F8FF'
        break
      case 'volcano':
        ctx.fillStyle = '#8B0000'
        break
      default:
        ctx.fillStyle = '#90EE90'
    }
    
    ctx.fillRect(0, 0, this.width, this.height)
  }
  
  /**
   * 绘制网格
   */
  drawGrid(ctx) {
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)'
    ctx.lineWidth = 1
    
    // 绘制垂直线
    for (let col = 0; col <= this.gridCols; col++) {
      const x = col * this.gridSize
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, this.height)
      ctx.stroke()
    }
    
    // 绘制水平线
    for (let row = 0; row <= this.gridRows; row++) {
      const y = row * this.gridSize
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(this.width, y)
      ctx.stroke()
    }
  }
  
  /**
   * 绘制建造区域
   */
  drawBuildableAreas(ctx) {
    ctx.fillStyle = 'rgba(0, 255, 0, 0.2)'
    
    for (let row = 0; row < this.gridRows; row++) {
      for (let col = 0; col < this.gridCols; col++) {
        const cell = this.grid[row][col]
        if (cell.type === 'buildable') {
          ctx.fillRect(cell.x, cell.y, this.gridSize, this.gridSize)
        }
      }
    }
  }
  
  /**
   * 绘制路径
   */
  drawPaths(ctx) {
    ctx.strokeStyle = '#8B4513'
    ctx.lineWidth = 20
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    
    this.paths.forEach(path => {
      if (path.length < 2) return
      
      ctx.beginPath()
      ctx.moveTo(path[0].x, path[0].y)
      
      for (let i = 1; i < path.length; i++) {
        ctx.lineTo(path[i].x, path[i].y)
      }
      
      ctx.stroke()
    })
  }
  
  /**
   * 绘制生成点和终点
   */
  drawSpawnAndEndPoints(ctx) {
    // 绘制生成点
    ctx.fillStyle = '#00ff00'
    this.spawnPoints.forEach(point => {
      ctx.beginPath()
      ctx.arc(point.x, point.y, 15, 0, Math.PI * 2)
      ctx.fill()
    })
    
    // 绘制终点
    ctx.fillStyle = '#ff0000'
    this.endPoints.forEach(point => {
      ctx.beginPath()
      ctx.arc(point.x, point.y, 15, 0, Math.PI * 2)
      ctx.fill()
    })
  }
  
  /**
   * 获取地图信息
   */
  getMapInfo() {
    return {
      width: this.width,
      height: this.height,
      gridSize: this.gridSize,
      gridCols: this.gridCols,
      gridRows: this.gridRows,
      pathCount: this.paths.length,
      spawnPoints: this.spawnPoints.length,
      endPoints: this.endPoints.length,
      buildableAreas: this.buildableAreas.length
    }
  }
}

// 导出模块
module.exports = GameMap
