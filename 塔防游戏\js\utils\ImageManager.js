/**
 * 简化的图片资源管理器
 * 只管理三张边框图片：蓝、紫、红
 */
class ImageManager {
  constructor() {
    // 图片缓存
    this.imageCache = new Map()

    // 加载状态
    this.loadingPromises = new Map()

    // 简化的图片路径配置
    this.borderPaths = {
      blue: 'assets/images/borders/border_blue.png',
      purple: 'assets/images/borders/border_purple.png',
      red: 'assets/images/borders/border_red.png'
    }

    // 星级图片路径配置
    this.starPaths = {
      purple: 'assets/images/stars/star_purple.png',
      yellow: 'assets/images/stars/star_yellow.png',
      red: 'assets/images/stars/star_red.png'
    }

    // 英雄背景图片路径配置
    this.heroBgPaths = {
      // 动态生成路径的基础模板
      getPath: (heroId) => `assets/images/heroes/hero_${heroId}_bg.png`
    }

    // 特效图片路径配置
    this.effectPaths = {
      rotation: 'assets/images/effects/rotation_effect.png',
      rotationReverse: 'assets/images/effects/rotation_effect_reverse.png',
      fade: 'assets/images/effects/fade_effect.png'
    }

    // 序列帧图片路径配置
    this.sequencePaths = {
      rare: 'assets/images/effects/rare_frame_{frame}.png',
      epic: 'assets/images/effects/epic_frame_{frame}.png',
      legendary: 'assets/images/effects/legendary_frame_{frame}.png',
      backButton: 'assets/images/effects/back_button_{frame}.png'
    }

    // 背景图片路径配置
    this.backgroundPaths = {
      heroScene: 'assets/images/backgrounds/hero_scene_bg.png'
    }

    // 背景图片路径配置
    this.backgroundPaths = {
      heroScene: 'assets/images/backgrounds/hero_scene_bg.png'
    }
  }
  
  /**
   * 加载图片
   * @param {string} path 图片路径
   * @returns {Promise<Image>} 图片对象
   */
  loadImage(path) {
    // 如果已经缓存，直接返回
    if (this.imageCache.has(path)) {
      return Promise.resolve(this.imageCache.get(path))
    }
    
    // 如果正在加载，返回加载Promise
    if (this.loadingPromises.has(path)) {
      return this.loadingPromises.get(path)
    }
    
    // 创建加载Promise
    const loadPromise = new Promise((resolve, reject) => {
      const image = wx.createImage()
      
      image.onload = () => {
        // 加载成功，缓存图片
        this.imageCache.set(path, image)
        this.loadingPromises.delete(path)
        resolve(image)
      }
      
      image.onerror = (error) => {
        // 加载失败
        this.loadingPromises.delete(path)
        console.warn(`图片加载失败: ${path}`, error)
        reject(error)
      }
      
      // 开始加载
      image.src = path
    })
    
    // 缓存加载Promise
    this.loadingPromises.set(path, loadPromise)
    
    return loadPromise
  }
  
  /**
   * 加载单个边框图片
   * @param {string} color 颜色 (blue, purple, red)
   * @returns {Promise<Image>} 边框图片
   */
  async loadBorderImage(color) {
    const path = this.borderPaths[color]
    if (!path) {
      throw new Error(`未知的边框颜色: ${color}`)
    }

    return this.loadImage(path)
  }

  /**
   * 预加载所有边框图片
   * @returns {Promise<Object>} 所有边框图片
   */
  async preloadAllBorderImages() {
    const colors = ['blue', 'purple', 'red']
    const results = {}

    for (const color of colors) {
      try {
        results[color] = await this.loadBorderImage(color)
      } catch (error) {
        console.warn(`预加载${color}边框失败:`, error)
        results[color] = null
      }
    }

    return results
  }

  /**
   * 加载单个星级图片
   * @param {string} color 颜色 (purple, yellow, red)
   * @returns {Promise<Image>} 星级图片
   */
  async loadStarImage(color) {
    const path = this.starPaths[color]
    if (!path) {
      throw new Error(`未知的星级颜色: ${color}`)
    }

    return this.loadImage(path)
  }

  /**
   * 预加载所有星级图片
   * @returns {Promise<Object>} 所有星级图片
   */
  async preloadAllStarImages() {
    const colors = ['purple', 'yellow', 'red']
    const results = {}

    for (const color of colors) {
      try {
        results[color] = await this.loadStarImage(color)
      } catch (error) {
        console.warn(`预加载${color}星级失败:`, error)
        results[color] = null
      }
    }

    return results
  }
  
  /**
   * 获取缓存的图片
   * @param {string} path 图片路径
   * @returns {Image|null} 图片对象或null
   */
  getCachedImage(path) {
    return this.imageCache.get(path) || null
  }
  
  /**
   * 清理图片缓存
   */
  clearCache() {
    this.imageCache.clear()
    this.loadingPromises.clear()
  }

  /**
   * 加载英雄背景图片
   * @param {number} heroId 英雄ID
   * @returns {Promise<Image>} 英雄背景图片
   */
  async loadHeroBgImage(heroId) {
    const path = this.heroBgPaths.getPath(heroId)
    return this.loadImage(path)
  }

  /**
   * 加载特效图片
   * @param {string} effectName 特效名称
   * @returns {Promise<Image>} 特效图片
   */
  async loadEffectImage(effectName) {
    const path = this.effectPaths[effectName]
    if (!path) {
      throw new Error(`未知的特效: ${effectName}`)
    }
    return this.loadImage(path)
  }

  /**
   * 加载背景图片
   * @param {string} backgroundName 背景名称
   * @returns {Promise<Image>} 背景图片
   */
  async loadBackgroundImage(backgroundName) {
    const path = this.backgroundPaths[backgroundName]
    if (!path) {
      throw new Error(`未知的背景: ${backgroundName}`)
    }
    return this.loadImage(path)
  }

  /**
   * 加载序列帧图片
   * @param {string} quality 品质名称 (rare, epic, legendary)
   * @param {number} frameIndex 帧索引 (1-11)
   * @returns {Promise<Image>} 序列帧图片
   */
  async loadSequenceFrame(quality, frameIndex) {
    const pathTemplate = this.sequencePaths[quality]
    if (!pathTemplate) {
      throw new Error(`未知的品质: ${quality}`)
    }

    // 格式化帧编号为两位数字 (01, 02, 03, ...)
    const frameNumber = frameIndex.toString().padStart(2, '0')
    const path = pathTemplate.replace('{frame}', frameNumber)

    return this.loadImage(path)
  }

  /**
   * 批量加载某个品质的所有序列帧
   * @param {string} quality 品质名称 (rare, epic, legendary)
   * @returns {Promise<Image[]>} 序列帧图片数组
   */
  async loadSequenceFrames(quality) {
    const frames = []
    const frameCount = 10  // 每组10张图片

    for (let i = 1; i <= frameCount; i++) {
      try {
        const frame = await this.loadSequenceFrame(quality, i)
        frames.push(frame)
      } catch (error) {
        console.warn(`序列帧加载失败: ${quality}_frame_${i.toString().padStart(2, '0')}`, error)
        frames.push(null)  // 加载失败时推入null
      }
    }

    return frames
  }

  /**
   * 加载返回按钮序列帧
   * @returns {Promise<Image[]>} 返回按钮序列帧图片数组
   */
  async loadBackButtonFrames() {
    const frames = []
    const frameCount = 8  // 返回按钮8张图片

    for (let i = 1; i <= frameCount; i++) {
      try {
        const frame = await this.loadSequenceFrame('backButton', i)
        frames.push(frame)
      } catch (error) {
        console.warn(`返回按钮序列帧加载失败: back_button_${i.toString().padStart(2, '0')}`, error)
        frames.push(null)  // 加载失败时推入null
      }
    }

    return frames
  }

  /**
   * 获取边框图片路径
   * @param {string} color 颜色
   * @returns {string|null} 图片路径
   */
  getBorderImagePath(color) {
    return this.borderPaths[color] || null
  }
  
  /**
   * 检查图片是否已加载
   * @param {string} path 图片路径
   * @returns {boolean} 是否已加载
   */
  isImageLoaded(path) {
    return this.imageCache.has(path)
  }
  
  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      cachedImages: this.imageCache.size,
      loadingImages: this.loadingPromises.size,
      totalMemoryUsage: this.estimateMemoryUsage()
    }
  }
  
  /**
   * 估算内存使用量
   * @returns {number} 估算的内存使用量(KB)
   */
  estimateMemoryUsage() {
    let totalSize = 0
    for (const [path, image] of this.imageCache) {
      // 粗略估算：宽度 × 高度 × 4字节(RGBA)
      if (image.width && image.height) {
        totalSize += image.width * image.height * 4
      }
    }
    return Math.round(totalSize / 1024) // 转换为KB
  }
}

// 创建全局单例
const imageManager = new ImageManager()

// 导出模块
module.exports = imageManager
