/**
 * 性能监控器
 * 监控游戏性能指标
 */

class PerformanceMonitor {
  constructor() {
    // FPS监控
    this.frameCount = 0
    this.lastFrameTime = 0
    this.fps = 60
    this.fpsHistory = []
    this.maxFpsHistory = 60
    
    // 内存监控
    this.memoryUsage = {
      used: 0,
      total: 0,
      percentage: 0
    }
    
    // 渲染性能
    this.renderTime = 0
    this.updateTime = 0
    this.lastRenderStart = 0
    this.lastUpdateStart = 0
    
    // 性能警告阈值
    this.thresholds = {
      lowFps: 30,
      highMemory: 0.8, // 80%
      longRenderTime: 16.67, // 超过一帧时间
      longUpdateTime: 10
    }
    
    // 性能统计
    this.stats = {
      totalFrames: 0,
      droppedFrames: 0,
      averageFps: 60,
      peakMemory: 0,
      averageRenderTime: 0,
      averageUpdateTime: 0
    }
    
    // 警告回调
    this.onPerformanceWarning = null
    
    // 是否启用监控
    this.enabled = true
    
    // 监控间隔
    this.monitorInterval = null
    this.startMonitoring()
  }
  
  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
    }
    
    this.monitorInterval = setInterval(() => {
      this.updateMemoryUsage()
      this.checkPerformanceWarnings()
    }, 1000) // 每秒检查一次
  }
  
  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
  }
  
  /**
   * 更新FPS
   */
  updateFPS() {
    if (!this.enabled) return
    
    this.frameCount++
    this.stats.totalFrames++
    
    const currentTime = Date.now()
    
    if (currentTime - this.lastFrameTime >= 1000) {
      this.fps = this.frameCount
      this.frameCount = 0
      this.lastFrameTime = currentTime
      
      // 更新FPS历史
      this.fpsHistory.push(this.fps)
      if (this.fpsHistory.length > this.maxFpsHistory) {
        this.fpsHistory.shift()
      }
      
      // 计算平均FPS
      this.stats.averageFps = this.fpsHistory.reduce((sum, fps) => sum + fps, 0) / this.fpsHistory.length
      
      // 检查掉帧
      if (this.fps < this.thresholds.lowFps) {
        this.stats.droppedFrames++
      }
    }
  }
  
  /**
   * 开始渲染计时
   */
  startRenderTiming() {
    if (!this.enabled) return
    this.lastRenderStart = performance.now()
  }
  
  /**
   * 结束渲染计时
   */
  endRenderTiming() {
    if (!this.enabled) return
    
    const renderTime = performance.now() - this.lastRenderStart
    this.renderTime = renderTime
    
    // 更新平均渲染时间
    this.stats.averageRenderTime = (this.stats.averageRenderTime * 0.9) + (renderTime * 0.1)
  }
  
  /**
   * 开始更新计时
   */
  startUpdateTiming() {
    if (!this.enabled) return
    this.lastUpdateStart = performance.now()
  }
  
  /**
   * 结束更新计时
   */
  endUpdateTiming() {
    if (!this.enabled) return
    
    const updateTime = performance.now() - this.lastUpdateStart
    this.updateTime = updateTime
    
    // 更新平均更新时间
    this.stats.averageUpdateTime = (this.stats.averageUpdateTime * 0.9) + (updateTime * 0.1)
  }
  
  /**
   * 更新内存使用情况
   */
  updateMemoryUsage() {
    if (!this.enabled) return
    
    try {
      // 在微信小游戏中获取内存信息
      if (wx.getPerformance && wx.getPerformance().memory) {
        const memory = wx.getPerformance().memory
        this.memoryUsage.used = memory.usedJSHeapSize
        this.memoryUsage.total = memory.totalJSHeapSize
        this.memoryUsage.percentage = this.memoryUsage.used / this.memoryUsage.total
        
        // 更新峰值内存
        if (this.memoryUsage.used > this.stats.peakMemory) {
          this.stats.peakMemory = this.memoryUsage.used
        }
      }
    } catch (error) {
      // 忽略内存获取错误
    }
  }
  
  /**
   * 检查性能警告
   */
  checkPerformanceWarnings() {
    if (!this.enabled || !this.onPerformanceWarning) return
    
    const warnings = []
    
    // FPS警告
    if (this.fps < this.thresholds.lowFps) {
      warnings.push({
        type: 'low_fps',
        message: `FPS过低: ${this.fps}`,
        value: this.fps,
        threshold: this.thresholds.lowFps
      })
    }
    
    // 内存警告
    if (this.memoryUsage.percentage > this.thresholds.highMemory) {
      warnings.push({
        type: 'high_memory',
        message: `内存使用过高: ${Math.round(this.memoryUsage.percentage * 100)}%`,
        value: this.memoryUsage.percentage,
        threshold: this.thresholds.highMemory
      })
    }
    
    // 渲染时间警告
    if (this.renderTime > this.thresholds.longRenderTime) {
      warnings.push({
        type: 'long_render',
        message: `渲染时间过长: ${this.renderTime.toFixed(2)}ms`,
        value: this.renderTime,
        threshold: this.thresholds.longRenderTime
      })
    }
    
    // 更新时间警告
    if (this.updateTime > this.thresholds.longUpdateTime) {
      warnings.push({
        type: 'long_update',
        message: `更新时间过长: ${this.updateTime.toFixed(2)}ms`,
        value: this.updateTime,
        threshold: this.thresholds.longUpdateTime
      })
    }
    
    // 触发警告回调
    if (warnings.length > 0) {
      this.onPerformanceWarning(warnings)
    }
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      fps: {
        current: this.fps,
        average: Math.round(this.stats.averageFps),
        history: [...this.fpsHistory]
      },
      memory: {
        used: this.memoryUsage.used,
        total: this.memoryUsage.total,
        percentage: Math.round(this.memoryUsage.percentage * 100),
        peak: this.stats.peakMemory
      },
      timing: {
        render: Math.round(this.renderTime * 100) / 100,
        update: Math.round(this.updateTime * 100) / 100,
        averageRender: Math.round(this.stats.averageRenderTime * 100) / 100,
        averageUpdate: Math.round(this.stats.averageUpdateTime * 100) / 100
      },
      stats: {
        totalFrames: this.stats.totalFrames,
        droppedFrames: this.stats.droppedFrames,
        dropRate: this.stats.totalFrames > 0 ? 
          Math.round((this.stats.droppedFrames / this.stats.totalFrames) * 100) : 0
      }
    }
  }
  
  /**
   * 重置统计数据
   */
  resetStats() {
    this.stats = {
      totalFrames: 0,
      droppedFrames: 0,
      averageFps: 60,
      peakMemory: 0,
      averageRenderTime: 0,
      averageUpdateTime: 0
    }
    
    this.fpsHistory = []
  }
  
  /**
   * 设置性能警告回调
   */
  setWarningCallback(callback) {
    this.onPerformanceWarning = callback
  }
  
  /**
   * 设置监控阈值
   */
  setThresholds(thresholds) {
    Object.assign(this.thresholds, thresholds)
  }
  
  /**
   * 启用/禁用监控
   */
  setEnabled(enabled) {
    this.enabled = enabled
    
    if (enabled) {
      this.startMonitoring()
    } else {
      this.stopMonitoring()
    }
  }
  
  /**
   * 获取性能建议
   */
  getPerformanceSuggestions() {
    const suggestions = []
    const report = this.getPerformanceReport()
    
    if (report.fps.average < 45) {
      suggestions.push({
        type: 'fps',
        message: '帧率较低，建议减少同屏对象数量或降低特效质量',
        priority: 'high'
      })
    }
    
    if (report.memory.percentage > 70) {
      suggestions.push({
        type: 'memory',
        message: '内存使用较高，建议清理不必要的对象或使用对象池',
        priority: 'medium'
      })
    }
    
    if (report.timing.averageRender > 12) {
      suggestions.push({
        type: 'render',
        message: '渲染时间较长，建议优化绘制逻辑或减少绘制对象',
        priority: 'medium'
      })
    }
    
    if (report.stats.dropRate > 10) {
      suggestions.push({
        type: 'stability',
        message: '掉帧率较高，建议检查游戏循环逻辑',
        priority: 'high'
      })
    }
    
    return suggestions
  }
  
  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring()
    this.onPerformanceWarning = null
  }
}

// 创建全局性能监控器实例
const performanceMonitor = new PerformanceMonitor()

// 导出模块
module.exports = performanceMonitor
